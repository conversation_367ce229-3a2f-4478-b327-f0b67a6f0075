<ng-container *ngIf="type == 'modal'">
  <div class="flex-between bg-coal py-12 px-16">
    <h5 class="text-white">Share Details </h5>
    <a class="icon ic-close-secondary  ic-sm" (click)="modalRef.hide()"></a>
  </div>
  <div class="flex-center p-20 fw-600">
    <div class="flex-center-col">
      <div (click)="shareInfo('whatsApp', 0, template)" class="br-10 mb-8 bg-accent-green-30 cursor-pointer"
        id="clkLeadsWhatsapp" data-automate-id="clkLeadsWhatsapp">
        <span class="icon ic-whatsapp ic-large m-10"></span>
      </div>
      <div>{{'SHARE.whatsapp' | translate}}</div>
    </div>
    <div class="flex-center-col ml-20">
      <div (click)="shareInfo('email', 2, template)" class="br-10 mb-8 bg-dark-blue cursor-pointer" id="clkLeadsEmail"
        data-automate-id="clkLeadsEmail">
        <span class="icon ic-envelope ic-large m-10"></span>
      </div>
      <div>{{'SHARE.email' | translate}}</div>
    </div>
  </div>
</ng-container>
<ng-container *ngIf="!type">
  <div class="flex-center">
    <div [title]="whatsAppCount > 0 ? 'WhatsApp: ' + whatsAppCount : 'WhatsApp'"
      class="bg-accent-green-30 icon-badge position-relative" (click)="shareInfo('whatsApp', 0, template)">
      <span class="icon ic-whatsapp m-auto ic-xxs"></span>
      <span *ngIf="whatsAppCount" class="position-absolute ntop-14 text-xxs nright-14 dot dot-md bg-red mr-6">
        {{whatsAppCount >= 100 ? '99+' : whatsAppCount}}</span>
    </div>
    <div [title]="mailCount > 0 ? 'Email: ' + mailCount : 'Email'"
      class="bg-dark-blue icon-badge position-relative" (click)="shareInfo('email', 2, template)">
      <span class="icon ic-envelope m-auto ic-xxs"></span>
      <span *ngIf="mailCount" class="position-absolute ntop-14 text-xxs nright-14 dot dot-md bg-red mr-6">
        {{mailCount >= 100 ? '99+' : mailCount}}</span>
    </div>
  </div>
</ng-container>

<ng-template #template>
  <div class="h-100vh text-coal">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3 class="fw-semi-bold">Send {{shareDetails?.shareType}} message</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="px-20 h-100-90 scrollbar bg-white">
      <div class="fw-600 field-label">INFO</div>
      <div class="bg-secondary br-4 px-10 pt-4 pb-10">
        <div class="d-flex mt-10" *ngIf="!moduleName && leadData?.name">
          <div class="field-label m-0 fw-600 text-nowrap">Lead Name: </div>
          <div class="ml-6 text-truncate-2 break-all" [title]="leadData.name">{{leadData.name}}</div>
        </div>
        <div class="d-flex mt-10">
          <div class="field-label m-0 fw-600 text-nowrap">
            {{
            moduleName === 'project' || key === 'share-matching-projects' || key === 'share-matching-lead'
            ? 'Project: '
            : key === 'share-project-units' || key ==='share-units'
            ? 'Project Unit: '
            : 'Property: '
            }}
          </div>
          <div class="ml-6 text-truncate-2 break-all">{{getTitle()}}</div>
        </div>
        <ng-container *ngIf="key === 'share-matching-projects'">
          <div class="d-flex mt-10">
            <div class="field-label m-0 fw-600 text-nowrap">Project Unit:</div>
            <div class="ml-6 text-truncate-2 break-all">{{getUnitName()}}</div>
          </div>
        </ng-container>
      </div>
      <form [formGroup]="shareForm">
        <div
          *ngIf="(alternatePhone && shareDetails?.shareType==='whatsApp') || (shareDetails?.shareType==='whatsApp' && leadData?.alternateContactNo)">
          <h5 class="field-label">choose which number you want to <span
              class="text-lowercase">{{data?.shareType}}?</span>
          </h5>
          <div class="d-flex">
            <div class="align-center">
              <div class="form-check form-check-inline" (click)="selectedContact = contactPhone || leadData?.contactNo">
                <input type="radio" id="inpWhatsappPhoneNumber" data-automate-id="inpWhatsappPhoneNumber"
                  class="radio-check-input" formControlName="callType" value="primary" checked>
                <label class="fw-600 text-secondary cursor-pointer text-large" for="inpWhatsappPhoneNumber">
                  Primary
                </label>
              </div>
            </div>
            <div class="align-center">
              <div class="form-check form-check-inline"
                (click)="selectedContact = alternatePhone || leadData?.alternateContactNo">
                <input type="radio" id="inpWhatsappAltPhoneNumber" data-automate-id="inpWhatsappAltPhoneNumber"
                  class="radio-check-input" formControlName="callType" value="alternative">
                <label class="fw-600 text-secondary cursor-pointer text-large" for="inpWhatsappAltPhoneNumber">
                  {{'GLOBAL.alternate' | translate}}
                </label>
              </div>
            </div>
          </div>
        </div>

        <ng-container *ngIf="key === 'share-project' || key ==='share-matching-lead'">
          <h5 class="field-label">choose which template you want to <span class="text-lowercase">?</span></h5>
          <div class="d-flex mt-12 w-100">
            <div class="align-center w-50">
              <div class="form-check form-check-inline">
                <input type="radio" id="inpWhatsappProject" data-automate-id="inpWhatsappProject"
                  class="radio-check-input" formControlName="selectedTemplateType"
                  (click)="setSelectedTemplateType('project')" value="project">
                <label class="fw-600 text-secondary cursor-pointer text-large" for="inpWhatsappProject">
                  Project Template
                </label>
              </div>
            </div>
            <div class="align-center w-50">
              <div class="form-check form-check-inline">
                <input type="radio" id="inpWhatsappUnit" data-automate-id="inpWhatsappUnit" class="radio-check-input"
                  formControlName="selectedTemplateType" (click)="setSelectedTemplateType('unit')" value="unit">
                <label class="fw-600 text-secondary cursor-pointer text-large" for="inpWhatsappUnit">
                  Unit Template
                </label>
              </div>
            </div>
          </div>
        </ng-container>
        <ng-container
          *ngIf="(key !== 'bulk-share-project' && (key==='share-project' &&
          shareForm.get('selectedTemplateType')?.value ==='unit' || key==='share-matching-lead' && shareForm.get('selectedTemplateType')?.value ==='unit'))">
          <h5 class="field-label-req">{{ 'GLOBAL.select' | translate }} Unit</h5>
          <form-errors-wrapper [control]="shareForm.controls['selectedUnit']" label="Unit">
            <ng-select [virtualScroll]="true" formControlName="selectedUnit" [items]="unitTypes" bindLabel="name"
              (change)="onUnitChange($event)" placeholder="{{ 'GLOBAL.select' | translate }} Unit"></ng-select>
          </form-errors-wrapper>
        </ng-container>
        <h5 class="field-label">{{ 'GLOBAL.select' | translate }} {{ 'BULK_LEAD.template' | translate }}</h5>
        <ng-select [virtualScroll]="true" formControlName="selectedTemplate" [items]="templates" bindLabel="title"
          [ngClass]="{'pe-none blinking': isTemplatesLoading,'disabled': isTemplateSelectDisabled()}" (change)="onTemplateChange()"
          placeholder="{{ 'GLOBAL.select' | translate }} {{ 'BULK_LEAD.template' | translate }}">
        </ng-select>
        <div class="field-label">{{ 'LEADS.message'| translate }}</div>
        <div class="form-group">
          <textarea rows="8" formControlName="message" class="scrollbar" id="txtLeadMsg" data-automate-id="txtLeadMsg"
            placeholder="ex. Dear Nichola Ferrell,As per Our Telephonic Discussion your Site Visit is Confirm. This is a Reminder Message Please Confirm the time sir.

  Thankyou

  Regards,
  Vikram Surelia"></textarea>
        </div>
      </form>
    </div>
    <div class="flex-center">
      <button class="btn-coal w-260 mx-20" (click)="sendMessage()">{{ 'LEADS.send-message' | translate }}</button>
    </div>
  </div>
</ng-template>