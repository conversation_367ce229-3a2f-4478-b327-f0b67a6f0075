import { Attribute, ChangeDetectorRef, Component, EventEmitter, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { BehaviorSubject, debounceTime, distinctUntilChanged, filter, take, takeUntil } from 'rxjs';
import { ATTR_NO, ATTR_NO_ALL, BHK_TYPE, BR_NO, BR_NO_ALL, COMPLETIONSTATUS, EMPTY_GUID, FACING, FINISHINGTYPE, FURNISH_STATUS, lockInPeriodList, MONTHS, noticePeriodList, OFFERINGTYPE, PROPERTY_TYPE_IMAGES, SALE_TYPE_LIST, securityDepositDates, TRANSACTION_TYPE_LIST, UAE_EMIRATES, VALIDATION_CLEAR, VALIDATION_SET } from 'src/app/app.constants';
import { BHKType, EnquiryType, Facing, FolderNamesS3, FurnishStatus, PaymentFrequency, PossessionType, PropertyType, SaleType, TaxationMode } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { Property } from 'src/app/core/interfaces/leads.interface';
import { ListingAddress, PropertyDimension } from 'src/app/core/interfaces/property.interface';
import { assignToSort, changeCalendar, convertUrlsToLinks, formatBudget, generateEnumList, getAreaUnit, getAWSImagePath, getBHKDisplayString, getBRDisplayString, getLocalityDetailsByObj, getLocationDetailsByObj, getPropertyTypeId, onlyNumbers, onlyNumbersWithDecimal, onPickerOpened, patchFormControlValue, patchTimeZoneDate, setPropertySubTypeList, setTimeZoneDate, toggleValidation, validateAllFormFields } from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { FetchAllAttributes } from 'src/app/reducers/amenities-attributes/amenities-attributes.action';
import { getAllAttributes } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import { getGlobalAnonymousIsLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getProjectList, getProjectListIsLoading } from 'src/app/reducers/lead/lead.reducer';
import { AddToList, DeList, FetchAddress, FetchListingSourceWithId, FetchSyncListingSource } from 'src/app/reducers/listing-site/listing-site.actions';
import { getAddress, getListingSiteLoaders, getListingSourcesWithId, getSyncListingSource } from 'src/app/reducers/listing-site/listing-site.reducer';
import { FetchAreaUnitList, FetchPropertyAttributeList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnitIsLoading, getAreaUnits, getIsAttributesLoading } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { AddListing, AddWaterMark, FetchGalleryDropdownData, FetchListingById, FetchListingByIdSuccess, UpdateGallery, UpdateListing, UploadBrochure } from 'src/app/reducers/property/property.actions';
import { getBrochureList, getGalleryDropdownData, getGalleryDropdownDataIsLoading, getListingAddIsLoading, getListingUpdateIsLoading, getSelectedListing, getSelectedListingIsLoading, getWaterMarkImage } from 'src/app/reducers/property/property.reducer';
import { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';
import { getLocationsWithGoogleApi, getLocationsWithGoogleApiIsLoading } from 'src/app/reducers/site/site.reducer';
import { FetchAdminsAndReportees, FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getAdminsAndReportees, getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { PropertyService } from 'src/app/services/controllers/properties.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'add-listing',
  templateUrl: './add-listing.component.html',
})
export class AddListingComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('previewModal') previewModal: TemplateRef<any>;
  @ViewChild('deleteDocumentModal') deleteDocumentModal: TemplateRef<any>;
  @ViewChild('resolutionWarningModal') resolutionWarningModal: TemplateRef<any>;
  @ViewChild('fileUploadComponent') fileUploadComponent: any;
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  s3BucketUrl = env.s3ImageBucketURL;
  basicInfoForm: FormGroup;
  propertyDetailForm: FormGroup;
  attributeForm: FormGroup;
  listForm: FormGroup;
  publishForm: FormGroup;
  propertyTypeImages: Object = PROPERTY_TYPE_IMAGES;
  bhkNoList: Array<string>;
  bhkTypeList: Array<string> = BHK_TYPE;
  enquiredForList: Array<Object> = TRANSACTION_TYPE_LIST.slice(1, 3);
  saleTypeList: Array<Object> = SALE_TYPE_LIST;
  furnishStatusList: Array<{ dispName: string; value: string }> =
    FURNISH_STATUS;
  facingList: Array<{ displayName: string; value: string }> = FACING;
  projectList: Array<string> = [];
  isProjectListLoading: boolean = true;
  placesList: any[] = [];
  listingPlacesList: any[] = [];
  isPlacesListLoading: boolean = true;
  areaSizeUnits: Array<PropertyDimension> = [];
  isAreaSizeUnitsLoading: boolean = true;
  propertyTypeList: Array<Property> = JSON.parse(
    localStorage.getItem('propertyType')
  );
  propertySubTypeList: Array<{ displayName: string }> = [];
  masterAttributeList: any[] = [];
  isMasterAttributeListLoading: boolean = true;
  basicAttributes: Array<Attribute> = [];
  filteredBasicAttributes: Array<Attribute> = [];
  selectedAdditionalAttr: Array<string> = [];
  selectedAttributes: any;
  galleryImageArray: any;
  galleryVideoArray: any;
  selectedAmenities: Array<string> = [];
  selectedFile: Array<string> = [];
  stepOneSubmitted: boolean = false;
  showSubmitConfirmation: boolean = false;
  isGalleryCarouselVisible: boolean = false;
  isShowManualLocation: boolean = false;
  canViewOwner: boolean = false;
  canAssign: boolean = false;
  canList: boolean = false;
  dropdownOptions: number[] = Array.from({ length: 200 }, (_, i) => i + 1); // [1..200]
  budgetInWords: string = '';
  maintenanceCostInWords: string = '';
  downpaymentInWords: string = '';
  depositAmountInWords: string = '';
  commonAreaChargesInWords: string = '';
  currentStep: number = 1;
  imageIndex: number;
  propType: number;
  currentDelete: number;
  selectedPropertyInfo: any;
  isSelectedPropertyInfoLoading: boolean = true;
  activePropertyId: any;
  basicInfoFormValues: any;
  propInfoFormValues: any;
  attrFormValues: any;
  fileFormatToBeUploaded: string = 'application/pdf';
  fileName: string = '';
  patchFormControlValue = patchFormControlValue;
  formatBudget = formatBudget;
  onlyNumbers = onlyNumbers;
  onlyNumbersWithDecimal = onlyNumbersWithDecimal;
  getBHKDisplayString = getBHKDisplayString;
  getBRDisplayString = getBRDisplayString;
  onPickerOpened = onPickerOpened;
  getAWSImagePath = getAWSImagePath;
  convertUrlsToLinks = convertUrlsToLinks
  docList: any;
  galleryDropdownData: string[];
  isGalleryDropdownDataLoading: boolean = true;
  galleryPayload: any = {};
  galleryS3Paths: Array<string> = [];
  galleryS3PathsVid: Array<any> = [];
  galleryMapping: any = {};
  galleryOrderRanks: { [key: string]: number } = {};
  coverImgIndex = 0;
  coverImg: string = '';
  draggedImageIndex: number = -1;
  draggedOverImageIndex: number = -1;

  // Image resolution validation
  lowResolutionImages: any[] = [];
  showResolutionWarning: boolean = false;
  pendingImageUpload: any[] = [];
  MIN_IMAGE_WIDTH: number = 600;
  MIN_IMAGE_HEIGHT: number = 800;
  dimensionCheckInProgress: boolean = false;
  imagesToProcess: any[] = [];
  links: any[] = [];
  currency: any[] = [];
  defaultCurrency: string = '';
  countryCode: any[];
  timeZone: any[];
  newUrl: string = '';
  listingUrl: string = '';
  preferredCountries = ['in'];
  hasInternationalSupport: boolean = false;
  isBrokerageUnitRequired: boolean = false;
  @ViewChild('ownerNoInput') ownerNoInput: any;
  userList: Array<any> = [];
  activeUsers: Array<any> = [];
  vidPathUrl: any;
  videoPayload: any = [];
  allAttributes: any;
  imageDimensions: { [key: string]: { width: number; height: number } } = {};

  addImage: AnimationOptions = {
    path: 'assets/animations/gallery-image.json',
  };
  inactiveUsers: any;
  addVideo: AnimationOptions = {
    path: 'assets/animations/gallery-video.json',
  };
  addDoc: AnimationOptions = {
    path: 'assets/animations/gallery-doc.json',
  };
  isGlobalSettingsLoading: boolean = true;
  attributeExpandedLists: { [key: string]: string[] } = {};

  waterMarkSettingsObj: any = {
    isWaterMarkEnabled: false,
    toAddWaterMark: false,
    fetchingFromGallery: false,
    watermarkLogo: '',
    watermarkOpacity: null,
    watermarkPosition: null,
    watermarkSize: null,
  };
  userData: any;
  currentDate: Date = new Date();
  offeringType = OFFERINGTYPE;
  uaeEmirates = UAE_EMIRATES;
  finishingType = FINISHINGTYPE;
  completionStatus = COMPLETIONSTATUS;
  numbers: number[] = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
  paymentList: any[];
  view360Url: any[] = [];
  selectedFileSize: any;
  listingSource: any[];
  listingSourceIsLoading: any;
  addressList: any[];
  filteredListingSource: any[] = [];
  addresses: any = {};
  filteredAddresses: any[][] = [];
  listingAddressisLoading: boolean;
  showLeftNav: boolean = true;
  taxationModeList = [
    { label: 'GST(incl)', value: TaxationMode.GSTInclusive },
    {
      label: 'GST(excl)',
      value: TaxationMode.GSTExclusive,
    },
  ];
  globalSettingsDetails: any;
  previewLanguage: string;
  deactiveUsers: any;
  isForNotes: boolean;
  listedSource: any[] = [];
  addressData: any;
  isPropertyUpdateIsLoading: boolean = false;
  sourceToggles: FormArray;
  isPropertyAddIsLoading: boolean = false;
  goodResolutionImages: any;
  selectedAvailableFrom: string = 'immediate';
  selectedPossessionDate: string = '';

  // Quality Score properties
  qualityScore: number = 0;

  securityDepositDates: any = securityDepositDates;
  lockInPeriodList: any = lockInPeriodList;
  noticePeriodList: any = noticePeriodList;
  selectedTotalFloor: number[] = [];
  selectedFloors: any[] = [];
  isAllSelected: boolean = false;
  selectedPermitType: string = 'dldPermitNumber';
  isImageUploading: boolean = false;
  isVideoUploading: boolean = false;
  isDocumentUploading: boolean = false;

  isOpenPossessionModal: boolean = false;
  selectedPossession: any;
  selectedMonthAndYear: Date;
  isValidPossDate: boolean;
  selectedMonth: any;
  selectedYear: any;
  propertyfinderLocationList: any[] = [];
  @ViewChild('dt5') dt5: OwlDateTimeComponent<any>;

  securityDepositInWords: string = '';

  get propertyContacts(): FormArray {
    return this.propertyDetailForm.get('propertyContacts') as FormArray;
  }

  get isPropertyPublished(): boolean {
    return this.selectedPropertyInfo?.sourceReferenceIds &&
      Object.keys(this.selectedPropertyInfo.sourceReferenceIds).length > 0;
  }

  get hasEnabledSources(): boolean {
    if (!this.sourceToggles || this.sourceToggles.length === 0) {
      return false;
    }
    return this.sourceToggles.controls.some(
      (control: AbstractControl) => control.get('isEnabled')?.value === true
    );
  }

  isSourcePublished(sourceId: string): boolean {
    return this.selectedPropertyInfo?.listingSources &&
      this.selectedPropertyInfo.listingSources.some((source: any) => source.id === sourceId);
  }

  get hasToggledOffPublishedSources(): boolean {
    if (!this.sourceToggles || this.sourceToggles.length === 0 || !this.selectedPropertyInfo?.listingSources) {
      return false;
    }

    return this.sourceToggles.controls.some((control: AbstractControl) => {
      const sourceId = control.get('sourceId')?.value;
      const isEnabled = control.get('isEnabled')?.value;
      const wasPublished = this.selectedPropertyInfo.listingSources.some((source: any) => source.id === sourceId);

      return wasPublished && !isEnabled;
    });
  }

  get hasNewEnabledSources(): boolean {
    if (!this.sourceToggles || this.sourceToggles.length === 0) {
      return false;
    }

    return this.sourceToggles.controls.some((control: AbstractControl) => {
      const sourceId = control.get('sourceId')?.value;
      const isEnabled = control.get('isEnabled')?.value;
      const wasPublished = this.selectedPropertyInfo?.listingSources?.some((source: any) => source.id === sourceId) || false;

      return isEnabled && !wasPublished;
    });
  }

  // Quality Score calculation method
  calculateQualityScore(): number {
    const POINTS_PER_FACTOR = 12.5;
    let totalScore = 0;

    // Safety check - return default if forms are not initialized
    if (!this.basicInfoForm || !this.propertyDetailForm) {
      return 0;
    }
    // 1. Description (750-2000 characters recommended)
    const description = this.basicInfoForm?.get('aboutProperty')?.value || '';
    const descriptionLength = description.length;
    if (descriptionLength >= 750 && descriptionLength <= 2000) {
      totalScore += POINTS_PER_FACTOR;
    }
    // 2. Images (Minimum 10 photos)
    const imageCount = this.galleryS3Paths?.length || 0;
    if (imageCount >= 10) {
      totalScore += POINTS_PER_FACTOR;
    }
    // 3. Image Diversity (All photos must be unique - no visually similar content)
    const uniqueImages = new Set(this.galleryS3Paths || []);
    const hasUniqueImages = uniqueImages.size === imageCount && imageCount > 0;
    if (hasUniqueImages) {
      totalScore += POINTS_PER_FACTOR;
    }
    // 4. Image Duplicates (No exact duplicate images)
    // This checks for exact duplicates (same file path/URL)
    const exactDuplicates = imageCount - uniqueImages.size;
    if (exactDuplicates === 0 && imageCount > 0) {
      totalScore += POINTS_PER_FACTOR;
    }
    // 5. Image Size (All images must meet 600x800 minimum dimensions)
    let validSizeImages = 0;
    let invalidSizeImages = 0;
    this.galleryS3Paths?.forEach(imagePath => {
      const dimensions = this.imageDimensions[imagePath];
      if (dimensions && dimensions.width >= 600 && dimensions.height >= 800) {
        validSizeImages++;
      } else {
        invalidSizeImages++;
      }
    });
    if (imageCount > 0 && invalidSizeImages === 0) {
      totalScore += POINTS_PER_FACTOR;
    }
    // 6. Location (Complete and specific location information)
    const hasLocationId = this.propertyDetailForm?.get('locationId')?.value;
    const hasManualLocation = this.isShowManualLocation &&
      this.propertyDetailForm?.get('enquiredCity')?.value &&
      this.propertyDetailForm?.get('enquiredState')?.value &&
      this.propertyDetailForm?.get('enquiredLocality')?.value;
    const hasCompleteLocation = hasLocationId || hasManualLocation;
    if (hasCompleteLocation) {
      totalScore += POINTS_PER_FACTOR;
    }
    // 7. Title (30-50 characters recommended)
    const title = this.basicInfoForm?.get('title')?.value || '';
    const titleLength = title.length;
    if (titleLength >= 30 && titleLength <= 50) {
      totalScore += POINTS_PER_FACTOR;
    }
    // 8. Verified (Listing must be marked as verified)
    const hasPermit = this.propertyDetailForm?.get('dldPermitNumber')?.value ||
      this.propertyDetailForm?.get('dtcmPermit')?.value;
    if (hasPermit) {
      totalScore += POINTS_PER_FACTOR;
    }
    // Calculate final percentage
    const percentage = Math.round(totalScore);
    this.qualityScore = percentage;
    return percentage;
  }

  // Getter for quality score display
  get qualityScoreDisplay(): string {
    return `${this.calculateQualityScore()}%`;
  }

  // Method to get simple score for internal use
  getQualityScoreValue(): number {
    return this.calculateQualityScore();
  }

  // Method to update quality score (for form change subscriptions)
  updateQualityScore(): void {
    try {
      const result = this.calculateQualityScore();
      this.qualityScore = result;
    } catch (error) {
      this.qualityScore = 0;
    }
  }

  constructor(
    private modalRef: BsModalRef,
    private modalRefClose: BsModalRef,
    private modalConfirm: BsModalRef,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private _store: Store<AppState>,
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    private imgService: BlobStorageService,
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    public router: Router,
    public _notificationService: NotificationsService,
    private shareDataService: ShareDataService,
    public trackingService: TrackingService,
    private propertyService: PropertyService
  ) { }

  ngOnInit(): void {
    this.shareDataService.showLeftNav$.subscribe(show => {
      this.showLeftNav = show;
    });

    this.bhkNoList = BR_NO;

    this.propertyTypeList = this.propertyTypeList.filter(
      (property: any) => property.displayName !== 'Agricultural'
    );

    this.paymentList = generateEnumList(PaymentFrequency);
    this._store
      .select(getWaterMarkImage)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!this.waterMarkSettingsObj.fetchingFromGallery) {
          return;
        }
        let waterMarkUrls: any = [];
        if (data) {
          data.forEach((url: any) => {
            let startIndex = url.indexOf(this.s3BucketUrl);
            if (startIndex !== -1) {
              let slicedString = url.slice(
                startIndex + this.s3BucketUrl.length
              );
              waterMarkUrls.push(slicedString);
            }
          });
        }

        this.galleryS3Paths?.push(...waterMarkUrls);
        if (this.galleryS3Paths.length === waterMarkUrls.length) {
          this.coverImgIndex = 0;
          this.coverImg = this.galleryS3Paths[0];
        } else {
          this.coverImg = this.galleryS3Paths[this.coverImgIndex];
        }
        this.galleryS3Paths?.forEach((img: string) => {
          this.galleryMapping = {
            ...this.galleryMapping,
            [img]: this.galleryMapping[img] || this.galleryDropdownData[0],
          };
        });
        this.updateImageOrderRanks();
      });

    this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isGlobalSettingsLoading = isLoading;
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
        (this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null),
          (this.currency = data?.countries?.length
            ? data.countries[0].currencies
            : null);
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.preferredCountries = data?.hasInternationalSupport
          ? data?.countries?.length
            ? [data.countries[0].code?.toLowerCase()]
            : ['in']
          : ['in'];
        this.timeZone = data?.defaultTimeZone;
        this.propertyDetailForm?.patchValue({
          currency:
            this.selectedPropertyInfo?.monetaryInfo?.currency ||
            this.defaultCurrency,
        });
        this.waterMarkSettingsObj.isWaterMarkEnabled =
          data?.leadProjectSetting?.isWaterMarksOnImagesEnabled;
        this.waterMarkSettingsObj.watermarkLogo =
          data?.leadProjectSetting?.waterMarkUrl;
        this.waterMarkSettingsObj.watermarkOpacity =
          data?.leadProjectSetting?.opacity;
        this.waterMarkSettingsObj.watermarkPosition =
          data?.leadProjectSetting?.waterMarkPosition;
        this.waterMarkSettingsObj.watermarkSize =
          data?.leadProjectSetting?.imageSize;
        // if (!this.selectedPropertyInfo) {
        //   this.waterMarkSettingsObj.toAddWaterMark = data?.leadProjectSetting?.isWaterMarksOnImagesEnabled
        // }
      });
    this.basicInfoForm = this.fb.group({
      enquiredFor: ['Rent', Validators.required],
      propertyType: [null, Validators.required],
      propertySubType: [null, Validators.required],
      noOfBHK: [null],
      title: ['', Validators.required],
      assignedTo: null,
      listingOnBehalf: null,
      propertySize: [null],
      areaUnit: [null],
      bhkType: [null],
      titleWithLanguage: [null],
      aboutProperty: [''],
      offeringType: [null, Validators.required],
      uaeEmirate: [null, Validators.required],
      finishingType: [null],
      completionStatus: [null, Validators.required],
      age: [null],
      aboutPropertyWithLanguage: [null],
      isPriceVissible: [false],
    });

    this.propertyDetailForm = this.fb.group({
      saleType: ['New'],
      expectedPrice: ['', Validators.required],
      downpayment: [null],
      globalRange: [null],
      globalDate: [null],
      currency: [this.defaultCurrency],
      isNegotiable: [false],
      locationId: [null],
      enquiredLocality: null,
      enquiredCity: null,
      enquiredState: null,
      enquiredCountry: null,
      enquiredPincode: null,
      postalCode: null,
      country: null,
      subCommunity: null,
      community: null,
      possessionDate: [null],
      notes: [''],
      // serviceCharges: [null],
      noOfChequesAllowed: [null],
      dtcmPermit: [null],
      dldPermitNumber: [null],
      licenseNo: [null], // Always disabled license number field
      paymentFrequency: [null],
      taxationMode: [0],
      propertyContacts: this.fb.array([
        this.createContactFormGroup()
      ]),
    });
    this.attributeForm = this.fb.group({
      numberOfBedrooms: [''],
      numberOfBathrooms: [''],
      numberOfKitchens: [''],
      furnishStatus: [null],
      facing: [null],
      numberOfParking: [null],
    });
    this.listForm = this.fb.group({
      addressId: [null],
    });
    this.onPermitTypeChange('dldPermitNumber');

    this._store.dispatch(new FetchPropertyAttributeList());
    this._store.dispatch(new FetchAreaUnitList());
    this._store.dispatch(new FetchLocationsWithGoogle());
    this._store.dispatch(new FetchAllAttributes());
    this._store.dispatch(new FetchListingSourceWithId())
    this._store.dispatch(new FetchAddress());
    this._store.dispatch(new FetchSyncListingSource());
    this._store
      .select(getListingSourcesWithId)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.listingSource = [...data];
      })

    this.metaTitle.setTitle('CRM | Properties');

    this._store
      .select(getAllAttributes)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const allAttr = data
          .map((attr: any) => ({
            ...attr,
            selected: this.selectedAdditionalAttr.includes(attr?.id)
          })).filter((attr: any) =>
            attr.isActive
          )
        // this.filteredAttributes = activeAttribute
        this.allAttributes = allAttr
        if (this.allAttributes?.length) {
          this.getPropertyAttributes();
        }
      })

    this._store
      .select(getIsAttributesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isMasterAttributeListLoading = isLoading
      });

    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this._store
      .select(getAreaUnitIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isAreaSizeUnitsLoading = isLoading
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this._store
      .select(getAddress)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.length) {
          this.addressData = data;
          data.forEach((item: any) => {
            if (item?.id && item.listingSourceAddresses?.length) {
              this.listingPlacesList[item.id] = [...item.listingSourceAddresses].map((address: any) => {
                const { towerName, subCommunity, community, city, id } = address;
                const addressName = [towerName, subCommunity, community, city]
                  .filter((val) => val)
                  .join(', ');
                return {
                  ...address,
                  location: addressName,
                };
              });
            }
          });
        }
      });

    this._store
      .select(getSyncListingSource)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.listedSource = data;
        this.publishForm = this.fb.group({
          sourceToggles: this.fb.array([]),
        });
        this.sourceToggles = this.publishForm.get('sourceToggles') as FormArray;
        if (this.listedSource && this.listedSource.length) {
          this.initializeSourceToggles();
        }
      })

    this._store
      .select(getListingUpdateIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPropertyUpdateIsLoading = isLoading;
      })

    this._store
      .select(getListingAddIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPropertyAddIsLoading = isLoading;
      })


    this._store
      .select(getListingSiteLoaders)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.listingSourceIsLoading = data?.listingSiteSourcesWithId;
        this.listingAddressisLoading = data?.addressWithId;
      })

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.userList = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.userList = assignToSort(this.userList, '');
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');

        this.deactiveUsers = data?.filter((user: any) => !user.isActive)?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.deactiveUsers = assignToSort(this.deactiveUsers, '');
      });

    this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.inactiveUsers = data?.filter((user: any) => !user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');

        this.deactiveUsers = data?.filter((user: any) => !user.isActive)?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.deactiveUsers = assignToSort(this.deactiveUsers, '');
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Properties.ViewOwnerInfo')) {
          this.canViewOwner = true;
        }
        if (permissions?.includes('Permissions.Properties.Assign')) {
          this.canAssign = true;
        }
        if (permissions?.includes('Permissions.Properties.PublishProperty')) {
          this.canList = true;
        }
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this._store.dispatch(new FetchUsersListForReassignment());
        } else {
          this._store.dispatch(new FetchAdminsAndReportees());
        }
      });
    this._store
      .select(getLocationsWithGoogleApiIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPlacesListLoading = isLoading;
      });
    this._store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {}).id) {
        this.activePropertyId = params.id;
        if (this.activePropertyId) {
          this._store.dispatch(new FetchListingById(this.activePropertyId));
        }
      }
    });

    this.headerTitle.setLangTitle(
      this.activePropertyId?.id || this.activePropertyId
        ? 'BUTTONS.edit-property'
        : 'BUTTONS.add-property'
    );

    this.basicInfoForm.valueChanges.subscribe((data: any) => {
      this.basicInfoFormValues = data;
      this.updateQualityScore();
    });

    this.basicInfoForm.get('assignedTo')?.valueChanges.subscribe((listingOnBehalf: any) => {
      this.getLicense(listingOnBehalf);
    });

    this.basicInfoForm.get('aboutProperty')?.valueChanges.subscribe(() => {
      this.updateQualityScore();
    });

    this.basicInfoForm.get('title')?.valueChanges.subscribe(() => {
      this.updateQualityScore();
    });

    this.propertyDetailForm.valueChanges.subscribe((data: any) => {
      this.propInfoFormValues = data;
      this.updateQualityScore();
    });

    this.propertyDetailForm.get('locationId')?.valueChanges.subscribe(() => {
      this.updateQualityScore();
    });

    this.propertyDetailForm.get('enquiredCity')?.valueChanges.subscribe(() => {
      this.updateQualityScore();
    });

    this.propertyDetailForm.get('enquiredState')?.valueChanges.subscribe(() => {
      this.updateQualityScore();
    });

    this.propertyDetailForm.get('dldPermitNumber')?.valueChanges.subscribe(() => {
      this.updateQualityScore();
    });

    this.propertyDetailForm.get('dtcmPermit')?.valueChanges.subscribe(() => {
      this.updateQualityScore();
    });

    // Calculate initial quality score
    setTimeout(() => {
      this.updateQualityScore();
    }, 100);

    this.basicInfoForm
      .get('propertySubType')
      .valueChanges.subscribe((pSubType: string) => {
        const ptype = this.basicInfoForm.controls['propertyType'].value;
        if (pSubType == 'Plot' && ptype == 'Residential') {
          toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'noOfBHK');
          toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'bhkType');
        } else if (pSubType != 'Plot' && ptype == 'Residential') {
          toggleValidation(VALIDATION_SET, this.basicInfoForm, 'noOfBHK', [
            Validators.required,
          ]);
          toggleValidation(VALIDATION_SET, this.basicInfoForm, 'bhkType', [
            Validators.required,
          ]);
        }
        toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'bhkType');
        this.filteredBasicAttributes = this.basicAttributes;
      });

    this.basicInfoForm
      .get('propertyType')
      .valueChanges.subscribe((pType: string) => {
        if (pType === 'Agricultural') {
          setTimeout(() => {
            this.basicInfoForm.patchValue({
              propertyType: null,
            });
          }, 100);
          return;
        }

        if (pType != 'Residential') {
          toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'noOfBHK');
          toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'bhkType');
        } else {
          toggleValidation(VALIDATION_SET, this.basicInfoForm, 'noOfBHK', [
            Validators.required,
          ]);
          toggleValidation(VALIDATION_SET, this.basicInfoForm, 'bhkType', [
            Validators.required,
          ]);
        }
        this.basicInfoForm.patchValue({
          propertySubType: '',
          noOfBHK: null,
          bhkType: null,
        });
        this.propType = PropertyType[pType as keyof typeof PropertyType];
        this.getPropertyAttributes();
      });

    this.basicInfoForm.controls['propertyType'].valueChanges.subscribe(
      (val: string) => {
        this.propertySubTypeList = setPropertySubTypeList(
          val,
          this.propertyTypeList
        );
      }
    );

    this.propertyDetailForm.get('currency').valueChanges.subscribe((val) => {
      this.budgetInWords = formatBudget(
        this.selectedPropertyInfo?.monetaryInfo?.expectedPrice ||
        this.propertyDetailForm.value.expectedPrice,
        val
      );

      this.downpaymentInWords = formatBudget(
        this.selectedPropertyInfo?.monetaryInfo?.downpayment ||
        this.propertyDetailForm.value.downpayment,
        val
      );
    });

    this.propertyDetailForm
      .get('expectedPrice')
      .valueChanges.subscribe((expectedPrice) => {
        this.budgetInWords = formatBudget(
          expectedPrice,
          this.propertyDetailForm.value.currency ||
          this.selectedPropertyInfo?.monetaryInfo?.currency ||
          this.defaultCurrency
        );
      });

    this.propertyDetailForm
      .get('downpayment')
      .valueChanges.subscribe((downpayment) => {
        this.downpaymentInWords = formatBudget(
          downpayment,
          this.propertyDetailForm.value.currency ||
          this.selectedPropertyInfo?.monetaryInfo?.currency
        );
      });
    // if (this.basicInfoForm.get('enquiredFor').value === 'Rent') {
    this.propertyDetailForm
      .get('expectedPrice')
      .valueChanges.subscribe((value) => {
        const paymentFrequencyControl =
          this.propertyDetailForm.get('paymentFrequency');
        if (value && this.basicInfoForm.get('enquiredFor').value === 'Rent') {
          paymentFrequencyControl.setValidators([Validators.required]);
        } else {
          paymentFrequencyControl.clearValidators();
        }
        paymentFrequencyControl.updateValueAndValidity();
      });
    // }

    this.propertyDetailForm.get('currency').valueChanges.subscribe((value) => {
      this.propertyDetailForm
        .get('currency')
        .setValue(value, { emitEvent: false });
    });

    this.propertyDetailForm.controls['globalRange'].valueChanges.subscribe(
      (value) => {
        if (value === 'Custom Date') {
          const possessionDate = this.propertyDetailForm.get('globalDate').value || null;
          if (possessionDate) {
            this.convertDateToMonth(possessionDate);
          }
        } else if (value === 'Immediate') {
          this.propertyDetailForm.controls['possessionDate'].setValue(new Date());
          this.propertyDetailForm.controls['globalDate'].setValue(new Date());
          this.selectedPossession = value;
          this.selectedMonth = null;
          this.selectedYear = null;
          this.selectedMonthAndYear = null;
          this.isValidPossDate = false;
          this.isOpenPossessionModal = false;
        }
      }
    );


    /* Fetching Places List */
    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => searchStr.length > 2)
      )
      .subscribe((searchStr: string) => {
        this._store.dispatch(new FetchLocationsWithGoogle(searchStr));
      });
    this._store.dispatch(new FetchGalleryDropdownData());

    this._store
      .select(getGalleryDropdownDataIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isGalleryDropdownDataLoading = isLoading;
      });

    this._store
      .select(getGalleryDropdownData)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.galleryDropdownData = data;
      });

    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isProjectListLoading = isLoading;
      });

    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getSelectedListingIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isSelectedPropertyInfoLoading: boolean) => {
        this.isSelectedPropertyInfoLoading = isSelectedPropertyInfoLoading;
      });

    this._store
      .select(getSelectedListing)
      .pipe(
        takeUntil(this.stopper),
        filter((propertyData: any) => Object.keys(propertyData)?.length && propertyData?.id === this.activePropertyId)
      )
      .subscribe((propertyData: any) => {
        this.selectedPropertyInfo = propertyData || {};

        // Calculate quality score after property data is loaded
        setTimeout(() => {
          this.updateQualityScore();
        }, 200);

        if (propertyData?.noOfBHK > 5) {
          this.bhkNoList = BR_NO_ALL;
        }
        propertyData.attributes.map((item: any) => {
          if (item?.value > 5) {
            this.attributeExpandedLists[item.attributeName] = Array.from(
              { length: 50 },
              (_, i) => (i + 1).toString()
            );
          } else if (item?.value > 5) {
            this.attributeExpandedLists[item.attributeName] = ATTR_NO_ALL;
          } else {
            this.attributeExpandedLists[item.attributeName] = ATTR_NO;
          }
        })
        const {
          enquiredFor,
          saleType,
          aboutProperty,
          furnishStatus,
          facing,
          notes,
          title,
          assignedTo,
          listingOnBehalf,
          bhkType,
          noOfBHK,
        } = propertyData;
        if (Array.isArray(propertyData.links)) {
          this.links = propertyData.links.filter((link: null) => link !== null);
        } else {
          this.links = [];
        }
        if (Array.isArray(propertyData?.view360Url)) {
          this.view360Url = propertyData?.view360Url.filter((link: null) => link !== null);
        } else {
          this.view360Url = [];
        }

        /* patch basic-info form values */
        if (propertyData?.propertyType?.displayName === 'Agricultural') {
          this._notificationService.warn('Agriculture property type is deprecated. Please select a valid type to proceed.');
        }
        this.basicInfoForm.patchValue({
          enquiredFor: EnquiryType[enquiredFor || 3],
          propertyType: propertyData?.propertyType?.displayName,
          areaUnit: getAreaUnit(
            propertyData.dimension?.areaUnitId,
            this.areaSizeUnits
          )?.id,
          propertySubType: propertyData?.propertyType?.childType?.displayName,
          noOfBHK: noOfBHK !== 0 ? noOfBHK?.toString() : null,
          bhkType: bhkType ? BHKType[bhkType] : null,
          title,
          assignedTo: assignedTo,
          listingOnBehalf: listingOnBehalf?.[0],
          aboutProperty,
          offeringType: propertyData?.offeringType > 0 ? propertyData?.offeringType : null,
          finishingType: propertyData?.finishingType > 0 ? propertyData?.finishingType : null,
          uaeEmirate: propertyData?.uaeEmirate > 0 ? propertyData?.uaeEmirate : null,
          completionStatus: propertyData?.completionStatus > 0 ? propertyData?.completionStatus : null,
          age: propertyData?.age > 0 ? propertyData?.age : null,
          isPriceVissible: propertyData?.monetaryInfo?.isPriceVissible,
          titleWithLanguage: propertyData?.titleWithLanguage,
          aboutPropertyWithLanguage: propertyData?.aboutPropertyWithLanguage,
        });
        this.basicInfoForm.patchValue({
          areaUnit: getAreaUnit(
            propertyData.dimension?.areaUnitId,
            this.areaSizeUnits
          )?.id,
        });
        this.basicInfoForm.controls['propertySize'].setValue(
          propertyData.dimension?.area == 0
            ? null
            : propertyData.dimension?.area
        );
        /* patch property detail form values */
        if (
          this.selectedPropertyInfo?.address &&
          !this.selectedPropertyInfo.address.placeId
        ) {
          const { city, state, subLocality, postalCode } =
            this.selectedPropertyInfo.address;
          if (city || state || subLocality || postalCode) {
            this.isShowManualLocation = true;
            const addressFields = ['enquiredLocality', 'enquiredCity', 'enquiredState', 'enquiredCountry'];
            if (this.isShowManualLocation) {
              addressFields.forEach(field => {
                toggleValidation(VALIDATION_SET, this.propertyDetailForm, field, [Validators.required]);
              });
            } else {
              addressFields.forEach(field => {
                toggleValidation(VALIDATION_CLEAR, this.propertyDetailForm, field);
              });
            }
          }
        }
        this.propertyDetailForm?.patchValue({
          saleType: SaleType[saleType],
          expectedPrice: propertyData.monetaryInfo?.expectedPrice,
          downpayment: propertyData.monetaryInfo?.downpayment,
          currency: propertyData.monetaryInfo?.currency
            ? propertyData.monetaryInfo?.currency
            : this.defaultCurrency,
          isNegotiable: propertyData.monetaryInfo?.isNegotiable,
          enquiredLocality:
            getLocalityDetailsByObj(this.selectedPropertyInfo?.address) || null,
          enquiredCity: this.selectedPropertyInfo?.address?.city,
          enquiredState: this.selectedPropertyInfo?.address?.state,
          subCommunity: this.selectedPropertyInfo?.address?.subCommunity,
          community: this.selectedPropertyInfo?.address?.community,
          postalCode: this.selectedPropertyInfo?.address?.postalCode,
          country: this.selectedPropertyInfo?.address?.country,
          licenseNo: this.getLicense(assignedTo) !== 'Required' ? this.getLicense(assignedTo) : null,
          possessionDate: patchTimeZoneDate(
            propertyData.possessionDate,
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
          globalRange:
            propertyData.possesionType === 0 ? null
              : PossessionType[propertyData.possesionType],
          globalDate: propertyData.enquiry?.possessionDate || null,
          notes,
          noOfChequesAllowed:
            propertyData?.monetaryInfo?.noOfChequesAllowed || null,
          dtcmPermit: propertyData?.compliance?.type === 2 ? propertyData?.compliance?.listingAdvertisementNumber : null,
          dldPermitNumber: propertyData?.compliance?.type === 1 ? propertyData?.compliance?.listingAdvertisementNumber : null,
          paymentFrequency:
            propertyData?.monetaryInfo?.paymentFrequency || null,
          taxationMode: propertyData?.taxationMode || 0,
          propertyContacts: this.patchPropertyContacts(propertyData?.propertyOwnerDetails),
        });
        if (this.selectedPropertyInfo?.serialNo) {
          this.sourceToggles.controls.findIndex(
            (control: AbstractControl) => control.get('referenceId').setValue(this.selectedPropertyInfo.serialNo)
          )
        }
        if (propertyData?.listingSourceAddresses?.length) {
          this.patchListingAddresses(propertyData?.listingSourceAddresses)
        }
        if (propertyData?.sourceReferenceIds && Object.keys(propertyData.sourceReferenceIds).length > 0) {
          if (this.sourceToggles && this.sourceToggles.length > 0) {
            this.patchSourceReferenceIds(propertyData.sourceReferenceIds);
          } else {
            setTimeout(() => {
              if (this.sourceToggles && this.sourceToggles.length > 0) {
                this.patchSourceReferenceIds(propertyData.sourceReferenceIds);
              }
            }, 500);
          }
        }

        if (propertyData?.compliance?.type === 1) {
          this.selectedPermitType = 'dldPermitNumber';
        } else if (propertyData?.compliance?.type === 2) {
          this.selectedPermitType = 'dtcmPermit';
        }

        if (propertyData?.possesionType === 6) {
          this.selectedAvailableFrom = 'immediate';
          this.selectedPossessionDate = '';
        } else if (propertyData?.possesionType === 5) {
          this.selectedAvailableFrom = 'selectDate';
          this.convertDateToMonth(propertyData?.possessionDate);
          if (propertyData?.possessionDate) {
            const possessionDate = new Date(propertyData.possessionDate);
            const options: Intl.DateTimeFormatOptions = {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            };
            this.selectedPossessionDate = possessionDate.toLocaleDateString('en-US', options);
          }
        } else {
          this.selectedAvailableFrom = 'immediate';
          this.selectedPossessionDate = '';
        }
        this.onPermitTypeChange(this.selectedPermitType);

        if (this.selectedPropertyInfo?.address?.placeId) {
          this.propertyDetailForm.patchValue({
            ...this.propertyDetailForm.value,
            enquiredState: null,
            enquiredCity: null,
            subCommunity: null,
            community: null,
            postalCode: null,
            country: null,
            enquiredLocality: null,
            locationId: {
              id: EMPTY_GUID,
              location: getLocationDetailsByObj(
                this.selectedPropertyInfo?.address
              ),
              placeId: this.selectedPropertyInfo?.address?.placeId,
            },
          });
          this.placesList = [
            ...this.placesList,
            {
              id: EMPTY_GUID,
              location: getLocationDetailsByObj(
                this.selectedPropertyInfo?.address
              ),
              placeId: this.selectedPropertyInfo?.address?.placeId,
            },
          ];
        }
        /* patch attribute form values */
        const propertyAttributes = propertyData.attributes;
        this.selectedAdditionalAttr = [];
        const savedAttributesSelection = propertyAttributes?.reduce(
          (acc: any, cur: any) => {
            const [attr]: any = this.allAttributes.filter(
              (item: any) => item?.id === cur.masterPropertyAttributeId
            );
            const isAdditional =
              attr?.attributeType.toLowerCase()?.replace(/\s+/g, '') ===
              'additional';
            if (isAdditional) {
              this.selectedAdditionalAttr.push(cur.masterPropertyAttributeId);
            } else {
              acc = {
                ...acc,
                [attr?.attributeName]: cur.value,
              };
            }
            return acc;
          },
          {}
        );

        if (!this.isAgricultural || !this.isPlot) {
          this.attributeForm.patchValue({
            furnishStatus:
              !furnishStatus &&
                !this.attributeForm?.get?.('furnishStatus')?.value
                ? null
                : this.attributeForm?.get?.('furnishStatus')?.value ||
                FurnishStatus[furnishStatus],
            facing:
              !facing && !this.attributeForm?.get?.('facing')?.value
                ? null
                : this.attributeForm?.get?.('facing')?.value || Facing[facing],
            numberOfBedrooms:
              this.attributeForm?.get?.('numberOfBedrooms')?.value ||
              savedAttributesSelection?.numberOfBedrooms,
            numberOfBathrooms:
              this.attributeForm?.get?.('numberOfBathrooms')?.value ||
              savedAttributesSelection?.numberOfBathrooms,
            numberOfParking: this.attributeForm?.get?.('numberOfParking')?.value ||
              savedAttributesSelection?.numberOfParking,
          });
        }
        let images = this.selectedPropertyInfo.imageUrls
          ? Object.entries(this.selectedPropertyInfo.imageUrls)
          : [];
        this.waterMarkSettingsObj.toAddWaterMark =
          this.selectedPropertyInfo?.isWaterMarkEnabled;
        this.galleryS3Paths = [];
        if (images.length > 0) {
          let allImages: { path: string, orderRank: number, isCover: boolean, category: string, width?: number, height?: number }[] = [];
          images.forEach((arr: any) => {
            let category = arr[0];
            let arrImg = arr[1];
            arrImg.forEach((img: any) => {
              const cleanedImagePath = img.imageFilePath.replace(this.s3BucketUrl, '');
              allImages.push({
                path: cleanedImagePath,
                orderRank: img.orderRank || 0,
                isCover: img.isCoverImage,
                category: category,
                width: img.width || 0,
                height: img.height || 0
              });
            });
          });
          allImages.sort((a, b) => a.orderRank - b.orderRank);
          const coverImage = allImages.find(img => img.isCover);
          allImages.forEach((img) => {
            this.galleryMapping[img.path] = img.category;
            this.galleryOrderRanks[img.path] = img.orderRank;
            this.imageDimensions[img.path] = {
              width: img.width || 0,
              height: img.height || 0
            };
          });
          if (coverImage && !this.galleryS3Paths.includes(coverImage.path)) {
            this.galleryS3Paths.push(coverImage.path);
            this.coverImgIndex = 0;
            this.coverImg = coverImage.path;
          }
          allImages.forEach((img) => {
            if (!img.isCover && !this.galleryS3Paths.includes(img.path)) {
              this.galleryS3Paths.push(img.path);
            }
          });

          if (!coverImage && this.galleryS3Paths.length > 0) {
            this.coverImgIndex = 0;
            this.coverImg = this.galleryS3Paths[0];
          }
        }
        let galleryVideos = this.selectedPropertyInfo?.videos;
        this.galleryS3PathsVid = [];
        this.videoPayload = [];
        galleryVideos?.map((item: any) => {
          if (!this.galleryS3PathsVid.includes(item.imageFilePath)) {
            this.galleryS3PathsVid?.push({
              name: item?.name,
              imageFilePath: item.imageFilePath,
            });
            this.videoPayload.push({
              name: item?.name,
              imageFilePath: item.imageFilePath,
              isCoverImage: item.isCoverImage,
              galleryType: 2,
            });
          }
        });

        !this.galleryS3Paths?.length ? (this.coverImg = '') : this.coverImg;
        this.getPropertyAttributes();
        this.getLicense(this.basicInfoForm.get('assignedTo')?.value);
      });

    this.onUnitChange('areaUnit');
    this.patchBasicInfoUnits()
  }

  possessionDateValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const globalRange = this.propertyDetailForm?.get('globalRange')?.value;
      if (globalRange === 'Immediate') {
        return null;
      }
      if (globalRange === 'Custom Date') {
        const value = control.value;
        if (!value) {
          return { required: true };
        }
      }
      return null;
    };
  }

  getSelectedCountryCodeContactNo(numType: string): any {
    switch (numType) {
      case 'ownerNoInput':
        return this.ownerNoInput?.selectedCountry;
      case 'contactPhoneInput':
      case 'alternatePhoneInput':
        if (this.globalSettingsDetails?.countries?.length && this.globalSettingsDetails?.countries[0]?.code) {
          return { dialCode: this.globalSettingsDetails.countries[0].code };
        }
        return { dialCode: 'IN' };
      default:
        return null;
    }
  }

  contactNumberValidator(numType: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      let inputValue: any;
      switch (numType) {
        case 'contactPhoneInput':
        case 'alternatePhoneInput':
          inputValue = control.value;
          break;
        default:
          return null;
      }
      if (!inputValue?.length) {
        return null;
      }
      let defaultCountry: CountryCode =
        this.getSelectedCountryCodeContactNo(numType)?.dialCode || 'IN';
      try {
        const validNumber = isPossiblePhoneNumber(inputValue, defaultCountry);
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  locationValidator() {
    this.handleNextClick();
  }

  onPermitTypeChange(type: string) {
    this.selectedPermitType = type;
  }

  createContactFormGroup(data?: any): FormGroup {
    return this.fb.group({
      name: [
        data?.name || data?.contactName || '',
        Validators.compose([
          Validators.maxLength(75),
          ValidationUtil.onlyAlphaNumericValidator,
        ]),
      ],
      phone: [data?.phone || data?.contactPhone || '', this.contactNumberValidator('contactPhoneInput')],
      alternatePhone: [data?.alternatePhone || '', this.contactNumberValidator('alternatePhoneInput')],
      email: [data?.email || data?.contactEmail || '', ValidationUtil.emailValidatorMinLength],
    });
  }

  patchPropertyContacts(contacts: any[]) {
    if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
      return;
    }
    while (this.propertyContacts.length) {
      this.propertyContacts.removeAt(0);
    }
    contacts.forEach(contact => {
      const mappedContact = {
        name: contact.name || contact.contactName,
        phone: contact.phone || contact.contactPhone,
        email: contact.email || contact.contactEmail,
        alternatePhone: contact.alternateContactNo || contact.alternatePhone
      };
      this.propertyContacts.push(this.createContactFormGroup(mappedContact));
    });
  }

  newContactAdded = false;

  addContact(): void {
    this.propertyContacts.push(this.createContactFormGroup());
    this.newContactAdded = true;
    setTimeout(() => {
      this.newContactAdded = false;
    }, 500);
  }

  removeContact(index: number): void {
    if (this.propertyContacts.length > 1) {
      this.propertyContacts.removeAt(index);
    }
  }

  otherAreaUnitValidator(key: string) {
    if (
      this.basicInfoForm.controls[key].value != null &&
      this.basicInfoForm.controls[key].value >= 0
    ) {
      this.basicInfoForm.controls[key + 'Unit'].setValidators([
        Validators.required,
      ]);
    } else {
      this.basicInfoForm.controls[key + 'Unit'].clearValidators();
    }
    this.basicInfoForm.controls[key + 'Unit'].updateValueAndValidity();
  }

  appendToBHKList(bhkNo: string) {
    if (bhkNo != '5+') return;
    this.bhkNoList = BR_NO_ALL;
  }

  appendToAttrList(attrNo: string, attributeName: string) {
    if (attrNo != '5+') return;
    this.attributeExpandedLists[attributeName] = [...ATTR_NO_ALL];
  }

  addInputField() {
    if (typeof this.newUrl === 'string' && this.newUrl.trim() !== '') {
      this.links = [...this.links, this.newUrl.trim()];
      this.newUrl = '';
    }
  }

  addListingField() {
    if (typeof this.listingUrl === 'string' && this.listingUrl.trim() !== '') {
      this.view360Url = [...this.view360Url, this.listingUrl.trim()];
      this.listingUrl = '';
    }
  }

  removeInputField(link: string) {
    const index = this.links.indexOf(link);
    if (index !== -1) {
      this.links.splice(index, 1);
    }
  }

  removeListingField(link: string) {
    const index = this.view360Url.indexOf(link);
    if (index !== -1) {
      this.view360Url.splice(index, 1);
    }
  }

  validateBudget(event: any) {
    const keyCode = event.keyCode;
    const excludedKeys = [8, 37, 39, 46];
    if (
      !(
        (keyCode >= 48 && keyCode <= 57) ||
        (keyCode >= 96 && keyCode <= 105) ||
        excludedKeys.includes(keyCode)
      )
    ) {
      event.preventDefault();
    }
  }

  getAttributesByType(attrType: string = 'additional') {
    const filteredAttributes = this.allAttributes
      .filter((item: any) => item.attributeType?.toLowerCase().replace(/\s+/g, '') === attrType)
      .filter((attr: any) => attr.basePropertyType.includes(this.propType));
    const propertySubType = this.basicInfoForm?.get('propertySubType')?.value;
    const propertyType = this.propertyDetailForm?.get('propertyType')?.value;
    if (!([
      "Office Space",
      "Co Working Office Space"
    ].includes(propertySubType) && propertyType !== "Commercial")) {
      return filteredAttributes.filter((data: any) =>
        ![
          'IsHavingReception',
          'IsHavingPantryRoom',
          'IsHavingServerRoom',
          'IsHavingTrainingRoom'
        ].includes(data?.attributeName)
      );
    }
    return filteredAttributes;
  }

  getPropertyAttributes(): any[] {
    const basicAttrs = this.getAttributesByType('basic');
    let { furnishStatus, facing, ...basicAttributeForm } =
      this.attributeForm.value;
    this.basicAttributes = basicAttrs.reduce((acc: any, cur: any) => {
      acc = [
        ...acc,
        {
          attributeName: cur.attributeName,
          attributeDisplayName: cur.attributeDisplayName,
          masterPropertyAttributeId: cur.id,
          value: basicAttributeForm[cur.attributeName]?.toString(),
        },
      ];
      return acc;
    }, []);
    const addAttributes = this.selectedAdditionalAttr.map((attrId: string) => {
      return {
        masterPropertyAttributeId: attrId,
      };
    });
    return [...this.basicAttributes, ...addAttributes];
  }

  onMinus(attributeName: string) {
    let value = this.attributeForm.controls[attributeName].value;
    if (value === undefined) {
      value = null;
      this.attributeForm.controls[attributeName].setValue(value);
    } else if (value > 0) {
      value--;
      this.attributeForm.controls[attributeName].setValue(value);
    }
  }

  onPlus(attributeName: string) {
    let value = this.attributeForm.controls[attributeName].value;
    if (value === undefined) {
      value = null;
      value++;
      this.attributeForm.controls[attributeName].setValue(value);
    } else if (value < 99 || value === undefined) {
      value++;
      this.attributeForm.controls[attributeName].setValue(value);
    }
  }

  isFormValid(form: FormGroup): boolean {
    if (form.valid) {
      return true;
    }
    validateAllFormFields(form);
    return false;
  }

  get isResidential(): boolean {
    return this.basicInfoForm.get('propertyType').value === 'Residential';
  }
  get isAgricultural(): boolean {
    return this.basicInfoForm.get('propertyType').value === 'Agricultural';
  }
  get isFarmHouse(): boolean {
    return this.basicInfoForm.get('propertySubType').value === 'Farm House';
  }
  get isRental(): boolean {
    return this.basicInfoForm.get('enquiredFor').value === 'Rent';
  }
  get f() {
    return this.basicInfoForm.controls;
  }
  get isPlot(): boolean {
    return this.basicInfoForm.get('propertySubType').value === 'Plot';
  }
  get canTakeNextStep(): boolean {
    switch (this.currentStep) {
      case 1:
        if (this.basicInfoForm.get('propertyType')?.value === 'Agricultural') {
          return false;
        }
        return this.isFormValid(this.basicInfoForm);
      case 2:
        return this.isFormValid(this.propertyDetailForm)
      case 3:
        return this.isFormValid(this.attributeForm);
      case 5:
        if (this.hasInvalidSourceToggles()) {
          this.sourceToggles.controls.forEach((control: AbstractControl) => {
            if (control.get('isEnabled').value) {
              control.get('locationId').markAsTouched();
              control.get('referenceId').markAsTouched();
            }
          });
          return false;
        }
        return true;
      default:
        return true;
    }
  }

  addMorePropertyInfo() {
    this.modalConfirm.hide();
    this.currentStep = this.isAgricultural || this.isPlot ? 4 : 3;
    this.attributeForm
      ?.get('numberOfBedrooms')
      .setValue(
        this.attributeForm?.get('numberOfBedrooms')?.value ||
        Math.round(Number(this.basicInfoForm?.value?.noOfBHK)) ||
        ''
      );
    this.showSubmitConfirmation = false;
  }

  addSelectedAmenities(event: any) {
    this.selectedAmenities = [...event];
    if (this.selectedPropertyInfo) {
      this.selectedPropertyInfo = {
        ...this.selectedPropertyInfo,
        amenities: [...new Set([...event])],
      };
    }
    this.cdr.detectChanges();
  }

  onAdditionalAttrSelection(selectedAttributes: any) {
    this.selectedAdditionalAttr = [...selectedAttributes];
  }

  openImage(i: number) {
    this.imageIndex = i;
    this.isGalleryCarouselVisible = true;
  }

  deleteImage(index: number) {
    const deletedImageUrl = this.galleryS3Paths[index];
    this.galleryS3Paths.splice(index, 1);
    if (index === this.coverImgIndex || this.galleryS3Paths.length === 0) {
      this.coverImgIndex = 0;
      this.coverImg = this.galleryS3Paths.length > 0 ? this.galleryS3Paths[0] : '';
    } else if (index < this.coverImgIndex) {
      this.coverImgIndex--;
    }
    delete this.galleryOrderRanks[deletedImageUrl];
    delete this.imageDimensions[deletedImageUrl];
    this.updateImageOrderRanks();
    if (this.selectedPropertyInfo?.id || this.activePropertyId?.id) {
      this.updateGalleryOrder();
    }
    // Recalculate quality score after image deletion
    this.updateQualityScore();
  }

  fileUploadToS3() {
    if (this.selectedFileSize > 15728640) {
      this._notificationService.warn(`File size should be less than 15 MB.`);
      return;
    }
    if (this.selectedFile?.[0]?.includes('data:')) {
      this.isDocumentUploading = true
      this.imgService
        .uploadImageBase64(this.selectedFile, FolderNamesS3.PropertyDocument)
        .pipe(takeUntil(this.stopper))
        .subscribe((response: any) => {
          this.isDocumentUploading = false

          let nameWithoutExtension = this.fileName.slice(
            0,
            this.fileName.lastIndexOf('.')
          );
          if (response.data.length) {
            if (this.docList) {
              let payload: any = {
                id: this.activePropertyId.id || this.activePropertyId,
                brochureDtos: [
                  ...this.docList,
                  {
                    name: nameWithoutExtension,
                    url: response.data?.[0] || '',
                  },
                ],
              };
              this._store.dispatch(
                new UploadBrochure(
                  this.activePropertyId.id || this.activePropertyId,
                  payload
                )
              );
            } else {
              let payload: any = {
                id: this.activePropertyId.id || this.activePropertyId,
                brochureDtos: [
                  {
                    name: nameWithoutExtension,
                    url: response.data?.[0] || '',
                  },
                ],
              };
              this._store.dispatch(
                new UploadBrochure(
                  this.activePropertyId.id || this.activePropertyId,
                  payload
                )
              );
            }
            this._store
              .select(getBrochureList)
              .pipe(takeUntil(this.stopper))
              .subscribe((data: any) => {
                this.docList = data;
              });
          }
        });
    }
  }

  initDeleteDocument(index: number) {
    this.modalRef = this.modalService.show(
      this.deleteDocumentModal,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
        }
      )
    );
    this.currentDelete = index;
  }

  removeDocument(index: number) {
    const updatedDocList = this.docList.filter(
      (doc: any, i: any) => i !== index
    );
    this.docList = updatedDocList;
    let payload: any = {
      id: this.activePropertyId.id || this.activePropertyId,
      brochureDtos: updatedDocList,
    };
    this._store.dispatch(
      new UploadBrochure(
        this.activePropertyId.id || this.activePropertyId,
        payload
      )
    );
    this.modalRef.hide();
  }

  handlePreviousClick() {
    if (this.currentStep > 1) {
      if (this.currentStep === 4 && (this.isAgricultural || this.isPlot)) {
        this.currentStep = this.currentStep - 2;
      } else {
        --this.currentStep;
      }
    }
  }

  setReferenceIdsToSerialNumber(): void {
    if (!this.sourceToggles || !this.selectedPropertyInfo?.serialNo) {
      return;
    }

    this.sourceToggles.controls.forEach((control: AbstractControl) => {
      const referenceId = control.get('referenceId').value;

      if (!referenceId) {
        control.get('referenceId').setValue(this.selectedPropertyInfo.serialNo);
      }
    });
  }

  handleNextClick() {
    if (this.currentStep === 2) {
      this.propertyDetailForm.controls['possessionDate'].markAsTouched();
    }
    Object.keys(this.propertyDetailForm.controls).forEach((field) => {
      const control = this.propertyDetailForm.get(field);
      if (control) {
        control.markAsTouched();
        if (control.invalid) {
          console.log(`Invalid field: ${field}`);
        }
      }
    });
    if (this.canTakeNextStep) {
      const goingToStep5 = this.currentStep === 4;

      ++this.currentStep;

      if (goingToStep5 && this.selectedPropertyInfo?.serialNo) {
        setTimeout(() => {
          this.setReferenceIdsToSerialNumber();
        }, 300);
      }
    }
  }

  handleStepsNavigation(targetStep: number) {
    if (this.currentStep > targetStep) {
      this.currentStep = targetStep;
    } else {
      // if (this.activePropertyId) {
      //   this.handlePropertyUpdate(this.propertyDetailTemplate)
      //   this._store.dispatch(new LoaderHide())
      // }

      switch (targetStep) {
        case 1:
          this.currentStep = targetStep;
          break;

        case 2:
          if (this.basicInfoForm.valid && this.basicInfoForm.get('propertyType')?.value !== 'Agricultural') {
            this.currentStep = targetStep;
          }
          break;

        case 3: if (this.activePropertyId && this.basicInfoForm.valid && this.propertyDetailForm.valid && this.basicInfoForm.get('propertyType')?.value !== 'Agricultural') {
          this.currentStep = targetStep;
        }
          break;
        case 4: if (this.activePropertyId && this.basicInfoForm.valid && this.propertyDetailForm.valid && this.basicInfoForm.get('propertyType')?.value !== 'Agricultural') {
          this.currentStep = targetStep;
        }
          break;
        case 5: if (this.activePropertyId && this.basicInfoForm.valid && this.propertyDetailForm.valid && this.basicInfoForm.get('propertyType')?.value !== 'Agricultural') {
          this.currentStep = targetStep;
        }
          break
      }
      const trackingSteps: Record<number, string> = {
        1: 'BasicInfo',
        2: 'PropertyInfo',
        3: 'Attributes',
        4: 'Amenities',
        5: 'Gallery'
      };
      if (trackingSteps[targetStep]) {
        this.trackingService.trackFeature(`Web.Property.AddProperty.${trackingSteps[targetStep]}.Click  `);
      }
    }
  }

  hasInvalidSourceToggles(): boolean {
    if (!this.sourceToggles || this.sourceToggles.length === 0) {
      return false;
    }

    let hasInvalid = false;
    this.sourceToggles.controls.forEach((control: AbstractControl) => {
      const isEnabled = control.get('isEnabled').value;
      const locationId = control.get('locationId');
      const refID = control.get('referenceId');

      if (isEnabled && (!locationId.value || locationId.invalid) && (!refID.value || refID.invalid)) {
        locationId.markAsTouched();
        refID.markAsTouched();
        hasInvalid = true;
      }
    });

    return hasInvalid;
  }

  hasAnyEnabledSource(): boolean {
    if (!this.sourceToggles || this.sourceToggles.length === 0) {
      return false;
    }

    return this.sourceToggles.controls.some(
      (control: AbstractControl) => control.get('isEnabled').value
    );
  }

  saveAsDraft(): void {
    if (this.sourceToggles && this.sourceToggles.length > 0) {
      let hasInvalid = false;

      this.sourceToggles.controls.forEach((control: AbstractControl) => {
        const isEnabled = control.get('isEnabled').value;
        const locationId = control.get('locationId');
        const refID = control.get('referenceId');

        if (isEnabled && (!locationId.value || locationId.invalid || !refID.value || refID.invalid)) {
          locationId.markAsTouched();
          refID.markAsTouched();
          hasInvalid = true;
        }
      });

      if (hasInvalid) {
        return;
      }
      if (this.hasToggledOffPublishedSources) {
        this._notificationService.warn(
          'You have toggled off listed portals. Click Publish to delist them, or toggle them back on to save as a draft.'
        );
        return;
      }

      if (this.hasNewEnabledSources) {
        this._notificationService.warn(
          'For save this property you need to publish or you need to toggle off portal which is not listed.'
        );
        return;
      }

    }

    const updateSuccess = this.updateSecondStep();

    if (updateSuccess) {
      this.router.navigate(['/properties/manage-listing']);
    }
  }

  getLicense(assignedTo: any[] | undefined): string | string[] {
    if (!assignedTo || assignedTo.length === 0) {
      return 'Required';
    }

    const licenses = assignedTo?.map(userId => {
      const user = this.userList?.find((user: any) => user.id === userId);
      return user ? user?.licenseNo : null;
    })
      .filter((license): license is string => license !== null);
    const licenseControl = this.propertyDetailForm.get('licenseNo');
    if (licenses) {
      licenseControl.setValue(licenses);
    }

    return licenses.length ? licenses : 'Required';
  }

  onPublish(): void {
    if (this.sourceToggles && this.sourceToggles.length > 0) {
      let hasInvalid = false;
      this.sourceToggles.controls.forEach((control: AbstractControl) => {
        const isEnabled = control.get('isEnabled').value;
        const locationId = control.get('locationId');
        const referenceId = control.get('referenceId');
        const sourceId = control.get('sourceId').value;

        if (isEnabled && (!locationId.value || locationId.invalid)) {
          locationId.markAsTouched();
          hasInvalid = true;
        }

        if (isEnabled && (!referenceId.value || referenceId.invalid)) {
          referenceId.markAsTouched();
          hasInvalid = true;
        }

        if (!isEnabled && this.selectedPropertyInfo?.sourceReferenceIds?.[sourceId]?.isEnabled) {
          const existingReferenceId = this.selectedPropertyInfo.sourceReferenceIds[sourceId]?.referenceId;
          if (!existingReferenceId || !referenceId.value || referenceId.invalid) {
            referenceId.markAsTouched();
            this._notificationService.warn('Please enter a valid reference ID to delist.');
            hasInvalid = true;
          }
        }
        if (!isEnabled && this.selectedPropertyInfo?.sourceReferenceIds?.[sourceId]?.isEnabled) {
          const existingLocations = this.selectedPropertyInfo?.listingSourceAddresses?.filter(
            (address: any) => address.listingSource?.id === sourceId
          );
          if (!existingLocations || !locationId.value || locationId.invalid) {
            referenceId.markAsTouched();
            this._notificationService.warn('Please enter location to delist.');
            hasInvalid = true;
          }
        }
      });

      if (hasInvalid) {
        return;
      }

      const enabledSources = this.sourceToggles.controls.filter(
        (control: AbstractControl) => control.get('isEnabled').value
      );

      // Validate listing requirements first
      const license = this.getLicense(this.basicInfoForm.value.assignedTo);
      const hasValidLicense = license !== 'Required';

      const hasValidAddress = this.selectedPropertyInfo?.listingSourceAddresses && this.selectedPropertyInfo.listingSourceAddresses.length > 0 || this.sourceToggles.length > 0;
      const hasValidPermit = (this.propertyDetailForm.value.dldPermitNumber || this.propertyDetailForm.value.dtcmPermit);

      let messages: string[] = [];
      if (!hasValidLicense && this.basicInfoForm.value.uaeEmirate === 1) {
        messages.push("Property does not have a valid broker number.");
      }
      if (!hasValidPermit && this.basicInfoForm.value.uaeEmirate === 1) {
        messages.push("Property does not have a valid permit number.");
      }
      if (!hasValidAddress) {
        messages.push("Property does not have a valid address.");
      }

      // If listing validations fail, don't save or list the property
      if (messages.length) {
        this._notificationService.warn(messages.join(' '));
        return;
      }

      // Only save and list the property if all validations pass
      const updateSuccess = this.updateSecondStep();
      if (!updateSuccess) {
        return;
      }

      // If all validations pass, proceed with listing
      const propertyIds = [this.selectedPropertyInfo?.id || this.activePropertyId];

      this._store.select(getListingUpdateIsLoading).pipe(
        filter(isLoading => !isLoading),
        take(1)
      ).subscribe(() => {
        if (enabledSources.length > 0) {
          const payload = {
            ids: propertyIds,
            listingSourceIds: enabledSources.map(
              (control: AbstractControl) => control.get('sourceId').value
            )
          };
          this._store.dispatch(new AddToList(payload));
        } else {
          // If no sources are enabled, call AddDelist
          const delistSourceIds = this.selectedPropertyInfo?.sourceReferenceIds
            ? Object.keys(this.selectedPropertyInfo.sourceReferenceIds)
              .filter((sourceId: string) => this.selectedPropertyInfo.sourceReferenceIds[sourceId]?.isEnabled)
            : [];

          const payload = {
            ids: propertyIds,
            listingSourceIds: delistSourceIds
          };
          this._store.dispatch(new DeList(payload));
        }
        this.router.navigate(['/properties/manage-listing']);
      });
    } else {
      this._notificationService.warn('No sources available to publish');
    }
  }

  updateSecondStep() {
    if (this.currentStep === 5 && this.hasInvalidSourceToggles()) {
      this.sourceToggles.controls.forEach((control: AbstractControl) => {
        if (control.get('isEnabled').value) {
          control.get('locationId').markAsTouched();
          control.get('referenceId').markAsTouched();
        }
      });
      return false;
    }

    this.updateImageOrderRanks();
    let mapping = Object.entries(this.galleryMapping);
    this.galleryPayload = {};
    mapping.forEach((item: any) => {
      if (this.galleryS3Paths.includes(item[0])) {
        if (!this.galleryPayload.hasOwnProperty(item[1])) {
          this.galleryPayload = {
            ...this.galleryPayload,
            [item[1]]: [],
          };
        }
        const dimensions = this.imageDimensions[item[0]] || { width: 0, height: 0 };
        this.galleryPayload[item[1]].push({
          imageFilePath: item[0],
          isCoverImage: this.galleryS3Paths[this.coverImgIndex] === item[0],
          galleryType: 1,
          orderRank: this.galleryOrderRanks[item[0]] || 0,
          imageSegregationType: 0,
          width: dimensions.width,
          height: dimensions.height
        });
      }
    });

    let payload = {
      isWaterMarkEnabled: this.waterMarkSettingsObj.toAddWaterMark,
      propertyId: this.selectedPropertyInfo?.id || this.activePropertyId?.id,
      imageUrls: {
        ...this.galleryPayload,
        videos: this.videoPayload,
      },
    };
    if (this.selectedPropertyInfo?.id || this.activePropertyId?.id) {
      this._store.dispatch(new UpdateGallery(payload));
    }

    this.handlePropertyUpdate({});
    return true;
  }

  uploadImage(e: any) {
    this.imagesToProcess = e;
    this.lowResolutionImages = [];
    this.pendingImageUpload = [];
    this.goodResolutionImages = [];

    if (this.imagesToProcess && this.imagesToProcess.length > 0) {
      this.imagesToProcess.forEach((image, index) => {
        const img = new Image();
        img.onload = () => {
          const dimensions = {
            width: img.width,
            height: img.height,
            fileName: `Image ${index + 1}`,
            file: image
          };
          const imageKey = typeof image === 'string' && image.startsWith('data:') ? image : `temp_${index}`;
          this.imageDimensions[imageKey] = {
            width: img.width,
            height: img.height
          };
          if (img.width < this.MIN_IMAGE_WIDTH || img.height < this.MIN_IMAGE_HEIGHT) {
            this.lowResolutionImages.push(dimensions);
          } else {
            this.goodResolutionImages.push(dimensions.file);
          }

          this.pendingImageUpload.push(dimensions);
          if (this.pendingImageUpload.length === this.imagesToProcess.length) {
            if (this.goodResolutionImages.length > 0) {
              this.processImageUpload(this.goodResolutionImages);
            }
            if (this.lowResolutionImages.length > 0) {
              this.showResolutionWarning = true;
              this.modalRef = this.modalService.show(this.resolutionWarningModal, {
                class: 'modal-600 modal-dialog-centered ip-modal-unset',
                keyboard: false
              });
            }
          }
        };
        img.onerror = () => {
          this.pendingImageUpload.push({
            fileName: `Image ${index + 1}`,
            file: image
          });
          if (this.pendingImageUpload.length === this.imagesToProcess.length) {
            const validImages = this.pendingImageUpload
              .filter(item => item.file)
              .map(item => item.file);

            if (validImages.length > 0) {
              this.processImageUpload(validImages);
            }
          }
        };
        if (typeof image === 'string' && image.startsWith('data:')) {
          img.src = image;
        }
      });
    }
  }

  processImageUpload(e: any) {
    this.galleryImageArray = e;
    if (
      this.galleryImageArray?.length &&
      !this.waterMarkSettingsObj.toAddWaterMark
    ) {
      if (this.selectedFileSize > 15728640) {
        this._notificationService.warn(`Image size should be less than 15 MB.`);
        return;
      }
      this.galleryImageArray = e;
      if (this.galleryImageArray?.length) {
        let imagesToBeUploadToS3Bucket = this.galleryImageArray.filter(
          (imagePath: string) => imagePath.startsWith('data:')
        );
        if (imagesToBeUploadToS3Bucket.length) {
          this.isImageUploading = true;
          this.imgService
            .uploadPropertyGallery(imagesToBeUploadToS3Bucket)
            .pipe(takeUntil(this.stopper))
            .subscribe(
              (res: any) => {
                if (res?.data) {
                  this.isImageUploading = false
                  let pathArr = res?.data;
                  pathArr?.forEach((path: string, index: number) => {
                    this.galleryS3Paths?.push(path);
                    const originalImageData = imagesToBeUploadToS3Bucket[index];
                    if (originalImageData && this.imageDimensions[originalImageData]) {
                      this.imageDimensions[path] = this.imageDimensions[originalImageData];
                      delete this.imageDimensions[originalImageData];
                    }
                  });
                  if (this.galleryS3Paths.length === pathArr.length) {
                    this.coverImgIndex = 0;
                    this.coverImg = this.galleryS3Paths[0];
                  } else {
                    this.coverImg = this.galleryS3Paths[this.coverImgIndex];
                  }
                  this.galleryS3Paths?.forEach((img: string, index: number) => {
                    this.galleryMapping = {
                      ...this.galleryMapping,
                      [img]:
                        this.galleryMapping[img] || this.galleryDropdownData[0],
                    };
                    if (!this.galleryOrderRanks[img]) {
                      this.galleryOrderRanks[img] = index;
                    }
                  });
                  this.updateImageOrderRanks();
                  // Recalculate quality score after image upload
                  this.updateQualityScore();
                  this._notificationService.success(
                    'Image uploaded successfully.'
                  );
                }
              },
              (error) => {
                this.isImageUploading = false
                this._notificationService.error(
                  'Failed to upload image. Please try again.'
                );
              }
            );
        }
      }
    }
  }

  removeLocation(type: any) {
    switch (type) {
      case 'location':
        this.propertyDetailForm.value.locationId = null;
        this.propertyDetailForm.value.enquiredLocality = null;
        this.propertyDetailForm.value.enquiredCity = null;
        this.propertyDetailForm.value.enquiredState = null;
        this.propertyDetailForm.value.subCommunity = null;
        this.propertyDetailForm.value.community = null;
        this.propertyDetailForm.value.postalCode = null;
        this.propertyDetailForm.value.country = null;
        break;
      case 'changeLocation':
        this.propertyDetailForm.patchValue({
          enquiredLocality: null,
          enquiredCity: null,
          enquiredState: null,
          subCommunity: null,
          community: null,
          postalCode: null,
          country: null,
        });
        break;
      case 'changeLocality':
        this.propertyDetailForm.patchValue({
          locationId: null,
        });
        break;
    }
  }

  uploadVideo(e: FileList) {
    let fileSize = e;
    if (fileSize && Array.isArray(fileSize)) {
      if (fileSize.some((size) => size[2] > 15728640)) {
        this._notificationService.warn(`Video size should be less than 15 MB.`);
        return;
      }
    }
    this.galleryVideoArray = e;
    if (this.galleryVideoArray?.length) {
      // let imagesToBeUploadToS3Bucket = this.galleryImageArray.filter(
      //   (imagePath: string) => imagePath.startsWith('data:')
      // );
      let imagesToBeUploadToS3Bucket = this.galleryVideoArray
        .filter((imagePath: string) => {
          return imagePath[0].startsWith('data:');
        })
        .map((imagePath: string) => imagePath[0]);
      if (imagesToBeUploadToS3Bucket.length) {
        this.isVideoUploading = true
        this.imgService
          .uploadPropertyGallery(imagesToBeUploadToS3Bucket)
          .pipe(takeUntil(this.stopper))
          .subscribe((res: any) => {
            if (res?.data) {
              this.isVideoUploading = false
              let pathArr = res?.data;
              pathArr?.map((path: string, index: number) => {
                this.vidPathUrl = getAWSImagePath(path);
                this.galleryS3PathsVid?.push({
                  imageFilePath: this.vidPathUrl,
                  name: this.galleryVideoArray[index][1],
                });
              });
              this.videoPayload = [];
              this.galleryS3PathsVid?.map((item: any) => {
                this.videoPayload.push({
                  name: item?.name,
                  imageFilePath: item?.imageFilePath,
                  isCoverImage: true,
                  galleryType: 2,
                });
              });
              this._notificationService.success('Video uploaded successfully.');
            }
          }, (error) => {
            this.isVideoUploading = false
            this._notificationService.error('Failed to upload video. Please try again.');
          });
      }
    }
  }


  deleteVideo(index: any) {
    this.galleryS3PathsVid.splice(index, 1);
    this.videoPayload = this.videoPayload.filter(
      (item: any, idx: any) => idx !== index
    );
  }

  handlePropertyUpdate(
    galleryImages: any) {
    const propertyType = this.basicInfoForm.controls['propertyType'].value;
    const propertySubType =
      this.basicInfoForm.controls['propertySubType'].value;
    const bhkType = this.basicInfoForm.controls['bhkType'].value;
    const id =
      getAreaUnit(
        this.basicInfoForm.controls['areaUnit'].value,
        this.areaSizeUnits
      )?.id || {};
    const furnishStatus = this.attributeForm.controls['furnishStatus'].value;
    const facing = this.attributeForm.controls['facing'].value;
    const listingAdvertisementNumber = this.selectedPermitType === 'dtcmPermit'
      ? this.propertyDetailForm.value.dtcmPermit
      : this.propertyDetailForm.value.dldPermitNumber ?? null;
    let propertyPayLoad: any = {
      ...this.selectedPropertyInfo,
      ...this.basicInfoForm.value,
      ...this.propertyDetailForm.value,

      title: this.basicInfoForm.value.title,
      titleWithLanguage: this.basicInfoForm.value.titleWithLanguage,
      assignedTo: this.basicInfoForm.value.assignedTo,
      listingOnBehalf: this.basicInfoForm.value.listingOnBehalf ? [this.basicInfoForm.value.listingOnBehalf] : null,
      saleType: SaleType[this.propertyDetailForm.value.saleType],
      enquiredFor: EnquiryType[this.basicInfoForm.value.enquiredFor],
      offeringType: this.basicInfoForm.value.offeringType,
      finishingType: this.basicInfoForm.value.finishingType,
      uaeEmirate: this.basicInfoForm.value.uaeEmirate,
      completionStatus: this.basicInfoForm.value.completionStatus,
      age: this.basicInfoForm.value.age,
      aboutProperty: this.basicInfoForm.value.aboutProperty,
      aboutPropertyWithLanguage:
        this.basicInfoForm.value.aboutPropertyWithLanguage,
      notes: this.propertyDetailForm.value.notes,
      furnishStatus: FurnishStatus[furnishStatus],
      status: this.selectedPropertyInfo?.status,
      shareCount: this.selectedPropertyInfo?.shareCount,
      possessionDate: setTimeZoneDate(
        this.propertyDetailForm.value.possessionDate,
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      possesionType: this.propertyDetailForm.value.globalRange
        ? PossessionType[this.propertyDetailForm.value.globalRange]
        : 0,
      facing: Facing[facing],
      noOfBHK:
        this.basicInfoForm.controls['propertySubType'].value != 'Plot' &&
          this.isResidential
          ? parseFloat(this.basicInfoForm.value.noOfBHK) || 0
          : 0,
      bhkType:
        this.basicInfoForm.controls['propertySubType'].value != 'Plot' &&
          this.isResidential
          ? BHKType[this.basicInfoForm.value.bhkType]
          : 0,
      monetaryInfo: {
        isNegotiable: this.propertyDetailForm.value.isNegotiable,
        expectedPrice: this.propertyDetailForm.value.expectedPrice,
        downpayment: this.propertyDetailForm.value.downpayment,
        currency: this.propertyDetailForm.value.currency
          ? this.propertyDetailForm.value.currency
          : this.defaultCurrency,
        noOfChequesAllowed: this.propertyDetailForm.value?.noOfChequesAllowed,
        isPriceVissible: this.basicInfoForm.value.isPriceVissible,
        paymentFrequency: this.propertyDetailForm.value.paymentFrequency,
      },
      propertyOwnerDetails: this.propertyContacts.value.map((contact: any) => ({
        name: contact.name,
        phone: contact.phone?.toString(),
        email: contact.email,
        alternateContactNo: contact.alternatePhone?.toString()
      })) || [],
      dtcmPermit:
        this.selectedPermitType === 'dtcmPermit'
          ? this.propertyDetailForm.value.dtcmPermit
          : null,
      dldPermitNumber:
        this.selectedPermitType === 'dldPermitNumber'
          ? this.propertyDetailForm.value.dldPermitNumber
          : null,
      ...(listingAdvertisementNumber && {
        compliance: {
          listingAdvertisementNumber,
          type: this.selectedPermitType === 'dldPermitNumber' ? 1 : 2,
        }
      }),
      dimension: {
        area: this.basicInfoForm.value.propertySize || 0,
        areaUnitId: id || EMPTY_GUID,
        currency: this.propertyDetailForm.value.currency
          ? this.propertyDetailForm.value.currency
          : this.defaultCurrency,
      },
      imageUrls: this.galleryPayload,
      brochures: this.docList || this.fileUploadToS3(),
      attributes: this.getPropertyAttributes(),
      amenities: this.selectedAmenities.length
        ? this.selectedAmenities
        : this.selectedPropertyInfo?.amenities,
      links: this.links,
      view360Url: this.view360Url,
      propertyTypeId: getPropertyTypeId(
        this.propertyTypeList,
        propertyType,
        propertySubType,
        bhkType
      ),
      listingAddresses: this.currentStep === 5
        ? this.getListingAddressesForPayload()
        : this.selectedPropertyInfo?.listingAddresses,
      sourceReferenceIds: this.currentStep === 5
        ? this.getSourceReferenceIdsForPayload()
        : this.selectedPropertyInfo?.sourceReferenceIds,
      taxationMode: this.propertyDetailForm.value.taxationMode,

    };
    if (!this.isShowManualLocation) {
      propertyPayLoad.address = {
        locationId:
          this.propertyDetailForm.value?.locationId?.id ??
          this.propertyDetailForm.value?.locationId?.id,
        placeId: this.propertyDetailForm.value?.locationId?.placeId
          ? this.propertyDetailForm.value?.locationId?.placeId
          : this.selectedPropertyInfo?.address?.placeId,
      };
    } else {
      propertyPayLoad.address = {
        subLocality:
          this.propertyDetailForm.value?.enquiredLocality ??
          this.propertyDetailForm.value?.enquiredLocality,
        city:
          this.propertyDetailForm.value?.enquiredCity ??
          this.propertyDetailForm.value?.enquiredCity,
        state:
          this.propertyDetailForm.value?.enquiredState ??
          this.propertyDetailForm.value?.enquiredState,
        subCommunity:
          this.propertyDetailForm.value?.subCommunity ??
          this.propertyDetailForm.value?.subCommunity,
        community:
          this.propertyDetailForm.value?.community ??
          this.propertyDetailForm.value?.community,
        country: this.propertyDetailForm.value?.country ??
          this.propertyDetailForm.value?.country,
        postalCode: this.propertyDetailForm.value?.postalCode ??
          this.propertyDetailForm.value?.postalCode,
      };
    }
    this.galleryPayload = {};
    let mapping = Object.entries(this.galleryMapping);
    mapping.forEach((item: any) => {
      if (this.galleryS3Paths.includes(item[0])) {
        if (!this.galleryPayload.hasOwnProperty(item[1])) {
          this.galleryPayload[item[1]] = [];
        }
        const dimensions = this.imageDimensions[item[0]] || { width: 0, height: 0 };
        this.galleryPayload[item[1]].push({
          imageFilePath: item[0],
          isCoverImage: this.galleryS3Paths[this.coverImgIndex] === item[0],
          width: dimensions.width,
          height: dimensions.height
        });
      }
    });
    propertyPayLoad.imageUrls = this.galleryPayload;
    propertyPayLoad.imageUrls = {
      ...propertyPayLoad.imageUrls,
      videos: this.videoPayload,
    };
    (propertyPayLoad.isWaterMarkEnabled =
      this.waterMarkSettingsObj.toAddWaterMark);
    if (this.activePropertyId) {

      this._store.dispatch(
        new UpdateListing(
          propertyPayLoad,
          propertyPayLoad.id ||
          this.activePropertyId.id ||
          this.activePropertyId
        )
      );
      this.closeModal();
    } else {
      this._store.dispatch(new AddListing(propertyPayLoad));
      this._store
        .select((state) => state.property.addedPropertyId)
        .pipe(takeUntil(this.stopper))
        .subscribe((propertyId: any) => {
          if (propertyId && Object.keys(propertyId).length > 0) {
            this.activePropertyId = propertyId;
          }
        })
    }
  }

  setImageCategory(e: any, url: any) {
    this.galleryMapping = {
      ...this.galleryMapping,
      [url]: e,
    };
  }

  onSetCoverImage(img: string, index: number) {
    this.galleryS3Paths.splice(index, 1);
    this.galleryS3Paths.unshift(img);
    this.coverImgIndex = 0;
    this.coverImg = img;
    this.updateImageOrderRanks();
    if (this.selectedPropertyInfo?.id || this.activePropertyId?.id) {
      this.updateGalleryOrder();
    }
  }

  onDragStart(index: number, event: DragEvent) {
    this.draggedImageIndex = index;
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/plain', index.toString());
    }
  }

  onDragOver(index: number, event: DragEvent) {
    event.preventDefault();
    this.draggedOverImageIndex = index;
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  }

  onDragLeave() {
    this.draggedOverImageIndex = -1;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    if (this.draggedImageIndex !== -1 && this.draggedOverImageIndex !== -1 && this.draggedImageIndex !== this.draggedOverImageIndex) {
      const draggedImage = this.galleryS3Paths[this.draggedImageIndex];
      this.galleryS3Paths.splice(this.draggedImageIndex, 1);
      this.galleryS3Paths.splice(this.draggedOverImageIndex, 0, draggedImage);
      if (this.draggedOverImageIndex === 0) {
        this.coverImgIndex = 0;
        this.coverImg = draggedImage;
      }
      else if (this.coverImgIndex === this.draggedImageIndex) {
        this.coverImgIndex = this.draggedOverImageIndex;
      }
      else if (
        (this.coverImgIndex > this.draggedImageIndex && this.coverImgIndex <= this.draggedOverImageIndex) ||
        (this.coverImgIndex < this.draggedImageIndex && this.coverImgIndex >= this.draggedOverImageIndex)
      ) {
        this.coverImgIndex += this.draggedImageIndex < this.draggedOverImageIndex ? -1 : 1;
      }
      this.updateImageOrderRanks();
      this.updateGalleryOrder();
    }
    this.draggedImageIndex = -1;
    this.draggedOverImageIndex = -1;
  }

  updateImageOrderRanks() {
    this.galleryS3Paths.forEach((url, index) => {
      this.galleryOrderRanks[url] = index;
    });
  }

  onSaveAndNextClick(): void {
    const updateSuccess = this.updateSecondStep();
    if (updateSuccess) {
      const interval = setInterval(() => {
        const isLoading = this.isPropertyAddIsLoading;
        if (!isLoading) {
          clearInterval(interval);
          if (this.activePropertyId.id) {
            this.activePropertyId = this.activePropertyId.id;
            this._store.dispatch(new FetchListingById(this.activePropertyId));
          }
          this.handleNextClick();
        }
      }, 100);
    }
  }

  onSaveAndNextClickStep2(): void {
    this.propertyDetailForm.controls['possessionDate'].markAsTouched();
    Object.keys(this.propertyDetailForm.controls).forEach((field) => {
      const control = this.propertyDetailForm.get(field);
      if (control) {
        control.markAsTouched();
      }
    });
    setTimeout(() => {
      const isStep2Valid = this.isFormValid(this.propertyDetailForm);
      const isStep1Valid = this.isFormValid(this.basicInfoForm);
      if (isStep1Valid && isStep2Valid) {
        const updateSuccess = this.updateSecondStep();
        if (updateSuccess) {
          const checkSaveComplete = () => {
            const isLoading = this.isPropertyAddIsLoading;
            if (!isLoading) {
              if (this.activePropertyId?.id) {
                this.activePropertyId = this.activePropertyId.id;
                this._store.dispatch(new FetchListingById(this.activePropertyId));
              }
              this.handleNextClick();
            } else {
              setTimeout(checkSaveComplete, 100);
            }
          };
          setTimeout(checkSaveComplete, 100);
        }
      } else {
        if (!isStep2Valid) {
          Object.keys(this.propertyDetailForm.controls).forEach((field) => {
            const control = this.propertyDetailForm.get(field);
            if (control && control.invalid) {
              console.log(`Invalid field: ${field}`, control.errors);
            }
          });
        }
      }
    }, 100);
  }

  updateGalleryOrder() {
    let mapping = Object.entries(this.galleryMapping);
    this.galleryPayload = {};
    mapping.forEach((item: any) => {
      if (this.galleryS3Paths.includes(item[0])) {
        if (!this.galleryPayload.hasOwnProperty(item[1])) {
          this.galleryPayload = {
            ...this.galleryPayload,
            [item[1]]: [],
          };
        }
        const dimensions = this.imageDimensions[item[0]] || { width: 0, height: 0 };

        this.galleryPayload[item[1]].push({
          imageFilePath: item[0],
          isCoverImage: this.galleryS3Paths[this.coverImgIndex] === item[0],
          galleryType: 1,
          orderRank: this.galleryOrderRanks[item[0]] || 0,
          imageSegregationType: 0,
          width: dimensions.width,
          height: dimensions.height
        });
      }
    });
    let payload = {
      isWaterMarkEnabled: this.waterMarkSettingsObj.toAddWaterMark,
      propertyId: this.selectedPropertyInfo?.id || this.activePropertyId?.id,
      imageUrls: {
        ...this.galleryPayload,
        videos: this.videoPayload,
      },
    };
    if (this.selectedPropertyInfo?.id || this.activePropertyId?.id) {
      this._store.dispatch(new UpdateGallery(payload));
    }
  }

  closeModal() {
    this.isValidPossDate = false;
    this.propertyDetailForm.controls['possessionDate'].markAsUntouched();
    if (
      this.propertyDetailForm.controls['globalRange'].value === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.propertyDetailForm.controls['globalRange'].setValue(null);
      this.propertyDetailForm.controls['possessionDate'].setValue(null);
      this.propertyDetailForm.controls['globalDate'].setValue(null);
      this.selectedPossession = null;
    }
    this.isOpenPossessionModal = false;
    this.modalRef?.hide();
    this.modalRefClose?.hide();
    this.modalConfirm?.hide();
  }

  goToManage() {
    this.router.navigate(['/properties/manage-listing']);
  }

  allAttributeSelectionCheck() {
    return (
      this.getAttributesByType().length > 0 &&
      this.selectedAdditionalAttr.length ===
      this.getAttributesByType().length - 2
    );
  }

  waterMarkImages(data: any) {
    let filedata = data;
    if (filedata && Array.isArray(filedata)) {
      if (filedata.some((size) => size.size > 5000000)) {
        return;
      }
    }

    if (this.waterMarkSettingsObj.toAddWaterMark) {
      let imgURL = this.waterMarkSettingsObj.watermarkLogo;
      let watermarkpayload = {
        Url: imgURL,
        files: filedata,
        WaterMarkPosition:
          this.waterMarkSettingsObj.watermarkPosition === 0
            ? '0'
            : this.waterMarkSettingsObj.watermarkPosition || 0,
        Opacity: this.waterMarkSettingsObj.watermarkOpacity || 100,
        Background: false,
        ImageSize: this.waterMarkSettingsObj.watermarkSize || 7,
      };
      this.waterMarkSettingsObj.fetchingFromGallery = true;
      this._store.dispatch(new AddWaterMark(watermarkpayload));
    }
  }

  onUnitChange(unit: any) {
    const areaUnit = this.basicInfoForm.get(unit).value;
    this.basicInfoForm.controls[unit]?.setValue(null);
    if (
      !this.basicInfoForm.get('areaUnit').value
    ) {
      this.basicInfoForm.controls['areaUnit'].setValue(areaUnit);
    } else {
      this.basicInfoForm.controls[unit].setValue(areaUnit);
    }
  }

  onSelectionChange(event: any, controlName: string) {
    const lastUser = event[event.length - 1];
    if (lastUser && !lastUser.isActive) {
      event.pop();
    }
    const newlySelectedItems = event.map((item: any) => item?.id);
    this.basicInfoForm?.get(controlName)?.setValue(newlySelectedItems);
  }

  addManualLocation() {
    this.isShowManualLocation = !this.isShowManualLocation;
    if (!this.isShowManualLocation) {
      this.propertyDetailForm.patchValue({
        enquiredLocality: null,
        enquiredCity: null,
        enquiredState: null,
        enquiredCountry: null
      });
    }
  }

  initializeSourceToggles() {
    while (this.sourceToggles.length) {
      this.sourceToggles.removeAt(0);
    }
    if (this.listedSource && this.listedSource.length) {
      this.listedSource.forEach(source => {
        this.sourceToggles.push(
          this.fb.group({
            sourceId: [source.id],
            isEnabled: [false],
            locationId: [null, null], // Will add validator when toggled
            sourceName: [source.displayName],
            referenceId: [null] // Field for reference ID
          })
        );
      });

      if (this.selectedPropertyInfo?.sourceReferenceIds) {
        this.patchSourceReferenceIds(this.selectedPropertyInfo.sourceReferenceIds);
      }
    }
  }

  patchBasicInfoUnits() {
    const defaultUnits = {
      areaUnit: this.basicInfoForm?.get('areaUnit')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit
    };
    this.basicInfoForm?.patchValue(defaultUnits);
  }

  monthChanged(event: any) {
    const selectedMonth = event.getMonth();
    const selectedYear = event.getFullYear();
    const lastDateOfMonth = new Date(selectedYear, selectedMonth + 1, 0);
    this.selectedMonthAndYear = lastDateOfMonth;
    this.propertyDetailForm.controls['globalDate'].setValue(this.selectedMonthAndYear);
    this.propertyDetailForm.controls['possessionDate'].setValue(this.selectedMonthAndYear);
    this.propertyDetailForm.controls['globalRange'].setValue('Custom Date');
    this.propertyDetailForm.controls['globalDate'].markAsDirty();
    this.propertyDetailForm.controls['possessionDate'].markAsDirty();
    this.propertyDetailForm.controls['globalRange'].markAsDirty();
    this.isValidPossDate = false;
    this.selectedMonth = this.selectedMonthAndYear.toLocaleString('default', {
      month: 'short',
    });
    this.selectedYear = this.selectedMonthAndYear.getFullYear().toString();
    this.selectedPossession = `${this.selectedMonth} ${this.selectedYear}`;
    this.isOpenPossessionModal = false;
    if (this.dt5) {
      this.dt5.close();
    }
  }

  closePossessionModal() {
    this.isValidPossDate = false;
    this.propertyDetailForm.controls['possessionDate'].markAsUntouched();
    if (
      this.propertyDetailForm.controls['globalRange'].value === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.propertyDetailForm.controls['globalRange'].setValue(null);
      this.propertyDetailForm.controls['possessionDate'].setValue(null);
      this.propertyDetailForm.controls['globalDate'].setValue(null);
      this.selectedPossession = null;
    }
    this.isOpenPossessionModal = false;
  }

  handlePossessionRangeChange(value: string): void {
    this.propertyDetailForm.get('globalRange')?.setValue(value);
    if (value === 'Custom Date') {
      this.currentDate = new Date();
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedMonthAndYear = null;
      this.propertyDetailForm.controls['globalDate'].setValue(null);
      this.propertyDetailForm.controls['possessionDate'].setValue(null);
      // Check if there's existing possession date to convert
      const existingDate = this.propertyDetailForm.get('globalDate').value ?? this.selectedPropertyInfo?.possessionDate;
      if (existingDate) {
        this.convertDateToMonth(existingDate);
      }
      this.isOpenPossessionModal = true;
    } else if (value === 'Immediate') {
      // For immediate possession, set possessionDate to current date
      const currentDate = new Date();
      this.propertyDetailForm.controls['possessionDate'].setValue(currentDate);
      this.propertyDetailForm.controls['globalDate'].setValue(currentDate);
      this.propertyDetailForm.controls['possessionDate'].markAsTouched();
      this.selectedPossession = value;
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedMonthAndYear = null;
      this.isValidPossDate = false;
      this.isOpenPossessionModal = false;
      this.propertyDetailForm.controls['possessionDate'].markAsDirty();
      this.propertyDetailForm.controls['possessionDate'].markAsTouched();
      this.propertyDetailForm.controls['globalDate'].markAsDirty();
      this.selectedPossession = value;
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedMonthAndYear = null;
      this.isOpenPossessionModal = false;
    }
    this.propertyDetailForm.controls['globalRange'].markAsDirty();
  }

  openPreview(language: 'eng' | 'arabic', isForNotes: boolean): void {
    this.isForNotes = isForNotes
    this.previewLanguage = language;
    this.modalRef = this.modalService.show(this.previewModal, {
      class: 'modal-600 modal-dialog-centered ip-modal-unset',
    });
  }

  continueWithLowResImages() {
    this.modalRef.hide();
    this.showResolutionWarning = false;
    if (this.lowResolutionImages && this.lowResolutionImages.length > 0) {
      const lowResFiles = this.lowResolutionImages.map(item => item.file);
      this.processImageUpload(lowResFiles);
    }
    setTimeout(() => {
      this.pendingImageUpload = [];
      this.lowResolutionImages = [];
      this.goodResolutionImages = [];
      this.imagesToProcess = [];
    }, 500);
  }

  cancelLowResImages() {
    this.closeModal();
    this.showResolutionWarning = false;
    this.pendingImageUpload = [];
    this.lowResolutionImages = [];
    this.imagesToProcess = [];
    setTimeout(() => {
      try {
        const fileInput = this.fileUploadComponent?.fileInputRef?.nativeElement
          || document.querySelector('input[type="file"]') as HTMLInputElement;
        if (fileInput) {
          fileInput.click();
          return;
        }
        const uploadButton = document.querySelector('.upload-button') as HTMLElement;
        if (uploadButton) {
          uploadButton.click();
          return;
        }
        this._notificationService.info("Please click on '+ Add Photos' to upload new images");
      } catch {
        this._notificationService.info("Please click on '+ Add Photos' to upload new images");
      }
    }, 300);
  }

  getPreviewContent(): string {
    if (this.isForNotes) {
      return this.propertyDetailForm.controls['notes'].value
    }
    if (this.previewLanguage === 'arabic') {
      return this.basicInfoForm.controls['aboutPropertyWithLanguage'].value || this.selectedPropertyInfo.aboutPropertyWithLanguage;
    }
    return this.basicInfoForm.controls['aboutProperty'].value || this.selectedPropertyInfo.aboutProperty;
  }

  // Helper method to check if source should be checked (both toggle enabled and exists in listingSources)
  isSourceChecked(sourceId: string, toggleIndex: number): boolean {
    const isToggleEnabled = this.sourceToggles.at(toggleIndex)?.get('isEnabled')?.value;
    return isToggleEnabled;
  }

  get canPublishToSources(): boolean {
    const hasEnabledSources = this.sourceToggles?.controls?.some(
      (control: AbstractControl) => control.get('isEnabled')?.value === true
    );
    const hasListedSources = this.selectedPropertyInfo?.listingSources?.length > 0;
    return hasEnabledSources || hasListedSources;
  }

  toggleSourceStatus(source: any) {
    const index = this.sourceToggles.controls.findIndex(
      (control: AbstractControl) => control.get('sourceId').value === source.id
    );
    if (index !== -1) {
      const sourceControl = this.sourceToggles.at(index);
      const currentStatus = sourceControl.get('isEnabled').value;
      const newStatus = !currentStatus;
      sourceControl.get('isEnabled').setValue(newStatus);
      const locationControl = sourceControl.get('locationId');
      const referenceIdControl = sourceControl.get('referenceId');
      if (newStatus) {
        locationControl.setValidators([Validators.required]);
        locationControl.markAsTouched();
        locationControl.updateValueAndValidity();
        referenceIdControl.setValidators([Validators.required]);
        referenceIdControl.markAsTouched();
        referenceIdControl.updateValueAndValidity();
      } else {
        locationControl.clearValidators();
        locationControl.updateValueAndValidity();
        referenceIdControl.clearValidators();
        referenceIdControl.updateValueAndValidity();
      }
      this.cdr.detectChanges();
    }
  }

  getListingAddressesForPayload(): ListingAddress[] {
    if (!this.sourceToggles?.length) {
      return [];
    }
    const propertyFinderId = this.listedSource.find((item: any) => item.displayName === 'PropertyFinder')?.id;
    return this.sourceToggles.controls
      .map((control: AbstractControl) => {
        const sourceId = control.get('sourceId').value;
        const locationId = control.get('locationId').value;
        if (!locationId || (!this.addressData.length && !this.propertyfinderLocationList.length)) {
          return null;
        }
        const sourceData = this.findSourceData(sourceId, locationId, propertyFinderId);
        if (!sourceData) {
          return null;
        }
        const addressDetails = this.findAddressDetails(sourceId, locationId, sourceData, propertyFinderId);
        if (!addressDetails) {
          return null;
        }
        return {
          sourceId: sourceId,
          towerName: addressDetails.towerName || '',
          subCommunity: addressDetails.subCommunity || '',
          community: addressDetails.community || '',
          city: addressDetails.city || '',
          locationId: sourceId === propertyFinderId ? locationId?.locationId : null,
          longitude: addressDetails.longitude || '',
          latitude: addressDetails.latitude || ''
        };
      })
      .filter(Boolean) as ListingAddress[];
  }

  private findSourceData(sourceId: any, locationId: any, propertyFinderId: any): any {
    if (sourceId === propertyFinderId) {
      if (!locationId) return null;
      if (typeof locationId === 'object' && locationId.location &&
        (locationId.towerName !== undefined || locationId.community !== undefined ||
          locationId.subCommunity !== undefined || locationId.city !== undefined)) {
        return locationId;
      }
      if (this.propertyfinderLocationList && this.propertyfinderLocationList.length > 0) {
        if (typeof locationId === 'object' && locationId.location) {
          return this.propertyfinderLocationList.find((item: any) =>
            item.location === locationId.location ||
            item.id === locationId.id
          ) || locationId;
        }
        if (typeof locationId === 'string') {
          return this.propertyfinderLocationList.find((item: any) =>
            item.location === locationId
          ) || { location: locationId };
        }
        return this.propertyfinderLocationList.find((item: any) =>
          item.id === locationId || item.id === locationId?.id
        ) || locationId;
      }
      return locationId;
    } else {
      return this.addressData.find((item: any) => item.id === sourceId);
    }
  }

  private findAddressDetails(sourceId: any, locationId: any, sourceData: any, propertyFinderId: any): any {
    if (sourceId === propertyFinderId) {
      // For PropertyFinder, we need to find the correct location object
      if (!locationId) return null;
      if (typeof locationId === 'object' && locationId.location &&
        (locationId.towerName !== undefined || locationId.community !== undefined ||
          locationId.subCommunity !== undefined || locationId.city !== undefined)) {
        return locationId;
      }
      if (this.propertyfinderLocationList && this.propertyfinderLocationList.length > 0) {
        if (typeof locationId === 'string') {
          const foundItem = this.propertyfinderLocationList.find((item: any) => item.location === locationId);
          if (foundItem) return foundItem;
        }

        const foundById = this.propertyfinderLocationList.find((item: any) =>
          item.id === locationId || item.id === locationId?.id
        );
        if (foundById) return foundById;
      }
      if (sourceData && (sourceData.towerName !== undefined || sourceData.community !== undefined ||
        sourceData.subCommunity !== undefined || sourceData.city !== undefined)) {
        return sourceData;
      }
      if (typeof locationId === 'object' && locationId.location) {
        return locationId;
      }
      if (typeof locationId === 'string') {
        return { location: locationId };
      }

      return sourceData;
    }
    return sourceData?.listingSourceAddresses?.find((address: any) => address.id === locationId);
  }

  getSourceReferenceIdsForPayload(): any {
    const payload: { [key: string]: { referenceId: string, isEnabled: boolean } } = {};
    if (this.sourceToggles && this.sourceToggles.length) {
      this.sourceToggles.controls.forEach((control: AbstractControl) => {
        const sourceId = control.get('sourceId').value;
        const referenceId = control.get('referenceId').value;
        const isEnabled = control.get('isEnabled').value;
        if (referenceId) {
          payload[sourceId] = {
            referenceId: referenceId,
            isEnabled: isEnabled
          };
        }
      });
    }
    return payload;
  }

  patchListingAddresses(data: any[]) {
    data?.forEach((address) => {
      const propertyFinderId = this.listedSource.find((item: any) => item.displayName === 'PropertyFinder')?.id;
      const sourceId = address.listingSource?.id;
      if (this.sourceToggles && this.sourceToggles.length > 0) {
        const toggleIndex = this.sourceToggles.controls.findIndex(
          (control: AbstractControl) => control.get('sourceId').value === sourceId
        );
        if (toggleIndex !== -1) {
          const toggle = this.sourceToggles.at(toggleIndex);
          const locationControl = toggle.get('locationId');
          const isEnabled = toggle.get('isEnabled')?.value;
          if (sourceId === propertyFinderId) {
            // For PropertyFinder, we need to create a location object with the same format as expected by the ng-select
            if (address.location) {
              // If address already has a location property, use it directly
              locationControl.setValue(address.location);
            } else {
              // Otherwise, construct a location object from address components
              const { towerName, subCommunity, community, city } = address;
              const addressName = [towerName, subCommunity, community, city]
                .filter((val) => val)
                .join(', ');
              // Fetch PropertyFinder locations to find a match
              this.propertyService.getpropertyfinderLocationList({
                Search: addressName,
                SourceId: propertyFinderId
              }).subscribe((res: any) => {
                if (res?.data && res.data.length > 0) {
                  const locationMatch = res.data[0];
                  const locationObj = {
                    ...locationMatch,
                    location: addressName
                  };
                  if (!this.propertyfinderLocationList) {
                    this.propertyfinderLocationList = [];
                  }
                  const existingIndex = this.propertyfinderLocationList.findIndex(item =>
                    item.id === locationObj.id || item.location === locationObj.location
                  );
                  if (existingIndex === -1) {
                    this.propertyfinderLocationList.push(locationObj);
                  }
                  locationControl.setValue(locationObj);
                } else {
                  const locationObj = {
                    ...address,
                    location: addressName,
                    id: address.locationId || address.id
                  };
                  if (!this.propertyfinderLocationList) {
                    this.propertyfinderLocationList = [];
                  }
                  const existingIndex = this.propertyfinderLocationList.findIndex(item =>
                    (item.id && item.id === locationObj.id) ||
                    (item.location && item.location === locationObj.location)
                  );
                  if (existingIndex === -1) {
                    this.propertyfinderLocationList.push(locationObj);
                  }
                  locationControl.setValue(locationObj);
                }
              });
            }
          } else {
            locationControl.setValue(address.id);
          }
          if (isEnabled) {
            locationControl.setValidators([Validators.required]);
          } else {
            locationControl.clearValidators();
          }
          locationControl.updateValueAndValidity();
          if (this.selectedPropertyInfo?.sourceReferenceIds &&
            this.selectedPropertyInfo.sourceReferenceIds[sourceId]) {
            const refId = this.selectedPropertyInfo.sourceReferenceIds[sourceId];
            toggle.get('referenceId').setValue(refId);
          }
        }
      }
    });
  }

  patchSourceReferenceIds(sourceReferenceIds: { [key: string]: any }): void {
    if (!this.sourceToggles) {
      return;
    }
    this.sourceToggles.controls.forEach((control: AbstractControl) => {
      const sourceId = control.get('sourceId').value;
      const isEnabled = sourceReferenceIds?.[sourceId]?.isEnabled ?? control.get('isEnabled')?.value;
      control.get('isEnabled').setValue(isEnabled);
      const locationControl = control.get('locationId');
      const referenceControl = control.get('referenceId');
      if (sourceReferenceIds && sourceReferenceIds[sourceId]) {
        referenceControl.setValue(sourceReferenceIds[sourceId]?.referenceId);
        if (this.selectedPropertyInfo?.listingSourceAddresses) {
          const sourceAddress = this.selectedPropertyInfo.listingSourceAddresses.find(
            (address: any) => address.listingSource?.id === sourceId
          );
          if (sourceAddress) {
            locationControl.setValue(sourceAddress.id);
          }
        }
      } else if (isEnabled && this.selectedPropertyInfo?.serialNo) {
        referenceControl.setValue(this.selectedPropertyInfo.serialNo);
      }
      // Apply conditional validators based on isEnabled
      if (isEnabled) {
        locationControl.setValidators([Validators.required]);
        referenceControl.setValidators([Validators.required]);
      } else {
        locationControl.clearValidators();
        referenceControl.clearValidators();
      }
      locationControl.updateValueAndValidity();
      referenceControl.updateValueAndValidity();
    });
  }

  convertDateToMonth(data: any) {
    if (!data) return;
    this.selectedMonthAndYear = data;
    this.selectedMonth = MONTHS[parseInt(data?.slice(5, 7), 10) - 1];
    this.selectedYear = parseInt(data?.slice(0, 4), 10);
    this.selectedPossession = this.selectedMonth
      ? this.selectedMonth + ' ' + this.selectedYear
      : null;
  }

  selectAvailableFrom(option: 'immediate' | 'selectDate') {
    this.selectedAvailableFrom = option;
    if (option === 'immediate') {
      this.propertyDetailForm.controls['globalRange'].setValue('Immediate');
      this.propertyDetailForm.controls['possessionDate'].setValue(new Date());
      this.propertyDetailForm.controls['globalDate'].setValue(new Date());
      this.selectedPossession = 'Immediate';
      this.selectedPossessionDate = ''; // Clear the date display
      this.isOpenPossessionModal = false;
      this.isValidPossDate = false;
    } else if (option === 'selectDate') {
      this.propertyDetailForm.controls['globalRange'].setValue('Custom Date');
      this.currentDate = new Date();
      this.isValidPossDate = false;
    }
    this.propertyDetailForm.controls['globalRange'].markAsDirty();
    this.propertyDetailForm.controls['possessionDate'].markAsDirty();
    this.propertyDetailForm.controls['globalDate'].markAsDirty();
  }

  onDateSelected(event: any) {
    if (event) {
      let selectedDate: Date;
      if (event instanceof Date) {
        selectedDate = event;
      } else if (event.value && event.value instanceof Date) {
        selectedDate = event.value;
      } else if (event.target && event.target.value) {
        selectedDate = new Date(event.target.value);
      } else if (typeof event === 'string') {
        selectedDate = new Date(event);
      } else {
        selectedDate = new Date(event);
      }
      this.propertyDetailForm.controls['possessionDate'].setValue(selectedDate);
      this.propertyDetailForm.controls['globalDate'].setValue(selectedDate);
      this.propertyDetailForm.controls['globalRange'].setValue('Custom Date');
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      };
      this.selectedPossessionDate = selectedDate.toLocaleDateString('en-US', options);
      this.selectedPossession = this.selectedPossessionDate;
      this.propertyDetailForm.controls['possessionDate'].markAsDirty();
      this.propertyDetailForm.controls['globalDate'].markAsDirty();
      this.propertyDetailForm.controls['globalRange'].markAsDirty();
      this.propertyDetailForm.controls['possessionDate'].markAsTouched();
      this.isValidPossDate = false;
    }
  }

  onPropertyfinderLocationInput(event: any) {
    const searchValue = event;
    let payload = {
      Search: searchValue,
      SourceId: this.listedSource?.filter((source: any) => source.displayName === 'PropertyFinder')[0].id
    }
    this.propertyService.getpropertyfinderLocationList(payload).subscribe((res: any) => {
      if (res?.data && res.data.length > 0) {
        this.propertyfinderLocationList = [...res?.data]?.map((item: any) => {
          const { towerName, subCommunity, community, city, id } = item;
          const addressName = [towerName, subCommunity, community, city]
            .filter((val) => val)
            .join(', ');
          return {
            ...item,
            location: addressName
          }
        });
      }
    })
  }

  ngOnDestroy() {
    this.waterMarkSettingsObj.fetchingFromGallery = false;
    this._store.dispatch(new FetchListingByIdSuccess({}))
    this.stopper.next();
    this.stopper.complete();
  }
}

