import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { getTenantName } from 'src/app/core/utils/common.util';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class GetLeadsService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string = '';
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'lead';
  }

  addQRLead(payload: any, templateId: string) {
    return this.http.post(
      `${this.serviceBaseUrl}/qr?templateId=${templateId}`,
      { ...payload, no_auth_needed: true }
    );
  }

  updateLeadStatus(resource: any, leadId: string) {
    return this.http.put(`${this.serviceBaseUrl}/status/${leadId}`, resource);
  }

  updateNotes(leadId: string, resource: any) {
    return this.http.put(`${this.serviceBaseUrl}/notes/${leadId}`, resource);
  }

  getNotesList(id: string, filterKeys: number) {
    const params = new HttpParams()
      .set('LeadId', id)
      .set('FilterKeys', filterKeys);
    return this.httpClient.get(`${this.serviceBaseUrl}/histories/filter?${params.toString()}`);
  }

  //  TODO: need to uncommented when teams are introduced
  // verifyOtp(resource: any) {
  //   return this.http.post(`${this.serviceBaseUrl}/UpdateLeadStatus/verify-otp`, resource);
  // }

  uploadExcel(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${this.serviceBaseUrl}/excel`, formData);
  }

  increaseShareCount(id: string) {
    return this.http.put(`${this.serviceBaseUrl}/UpdateShareCount/${id}`, '');
  }

  updateLeadTags(resource: any, id: string) {
    return this.http.put(`${this.serviceBaseUrl}/customflag/${id}`, resource);
  }

  updateBulkLeadStatus(resource: any) {
    return this.http.put(`${this.serviceBaseUrl}/status/bulk`, resource);
  }

  uploadMappedColumns(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/batch`, payload);
  }

  getExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/bulk/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  uploadMigrateMappingColumns(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/migrate/batch`, payload);
  }

  getMigrateExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/migrate/bulk/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  secondaryAssignLead(resource: any) {
    return this.http.put(
      `${this.serviceBaseUrl}/assign/secondary-users`,
      resource
    );
  }

  reassignLead(resource: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/assign`,
      resource
    );
  }

  reassignBoth(resource: any) {
    return this.http.put(`${this.serviceBaseUrl}/assign/new`, resource);
  }

  bulkReassignLead(resource: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/assign/multipleusers`,
      resource
    );
  }

  doesLeadExists(contactNo: any) {
    return this.http.get(
      `${this.serviceBaseUrl}/contactno?contactNo=${contactNo?.number}&countryCode=${contactNo?.countryCode}`
    );
  }

  uploadLeadDocument(id: string, payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/documents`, payload);
  }

  deletedLeadDocument(payload: any) {
    let headers: any = {
      body: payload,
    };
    return this.http.delete(`${this.serviceBaseUrl}/documents`, headers);
  }

  getProjectList(isWithArchive: boolean) {
    if (isWithArchive) {
      return this.http.get(`${this.serviceBaseUrl}/all/projects`);
    } else {
      return this.http.get(`${this.serviceBaseUrl}/projects`);
    }
  }

  getQRProjectList() {
    return this.http.get(`${this.serviceBaseUrl}/qr/projects`);
  }

  getPropertyList(isWithArchive: boolean) {
    if (isWithArchive) {
      return this.http.get(`${this.serviceBaseUrl}/all/properties`);
    } else {
      return this.http.get(`${this.serviceBaseUrl}/properties`);
    }
  }

  getQRPropertyList() {
    return this.http.get(`${this.serviceBaseUrl}/qr/properties`);
  }

  communicationCount(id: string, payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/contactcount/${id}`, payload);
  }

  communicationBulkCount(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/bulk/contactcount`, payload);
  }

  meetingOrVisitDone(payload: any) {
    return this.http.put(
      `${this.serviceBaseUrl}/meetingorsitevisitdone`,
      payload
    );
  }

  getLocations() {
    return this.http.get(`${this.serviceBaseUrl}/addresses`);
  }

  getLeadCities() {
    return this.http.get(`${this.serviceBaseUrl}/cities`);
  }

  getLeadStates() {
    return this.http.get(`${this.serviceBaseUrl}/states/new`);
  }

  getLeadCountries() {
    return this.http.get(`${this.serviceBaseUrl}/countries`);
  }

  getLeadSubCommunities() {
    return this.http.get(`${this.serviceBaseUrl}/subcommunities`);
  }

  getLeadCommunities() {
    return this.http.get(`${this.serviceBaseUrl}/communities`);
  }

  getLeadTowerNames() {
    return this.http.get(`${this.serviceBaseUrl}/towername`);
  }

  getLeadZones() {
    return this.http.get(`${this.serviceBaseUrl}/zones`);
  }

  deleteLeads(ids: string[]) {
    let headers: any = {
      body: {
        ids: ids,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/softdelete`, headers);
  }

  restoreLeads(ids: string[]) {
    return this.http.put(`${this.serviceBaseUrl}/restoreleads`, { ids: ids });
  }

  getSubSourceList() {
    return this.http.get(`${this.serviceBaseUrl}/subsource`);
  }

  bulkSource(resource: any) {
    return this.http.put(`${this.serviceBaseUrl}/source/bulk`, resource);
  }

  bulkProjects(resource: any) {
    return this.http.put(`${this.serviceBaseUrl}/projects/bulk`, resource);
  }

  getAgencyNames() {
    return this.http.get(`${this.serviceBaseUrl}/agencynames`);
  }

  getAgencyNamesAnonymous() {
    return this.http.get(`${this.serviceBaseUrl}/qr/agencynames`);
  }

  updateDuplicateAssign(resource: any) {
    return this.http.post(`${this.serviceBaseUrl}/duplicate/assign`, resource);
  }

  getDuplicateFeature() {
    return this.http.get(`${this.serviceBaseUrl}/duplicate/feature`);
  }

  exportLead(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/export/new/batch`, payload);
  }

  customExportLead(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/custom-filter-export`,
      payload
    );
  }

  getExportStatus(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/export/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  communicationMessage(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/message`, payload);
  }

  communicationBulkMessage(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/bulk/message`, payload);
  }

  getLeadStatusCount() {
    let tenant = getTenantName();
    const headers = new HttpHeaders().set('tenant', tenant);
    return this.http.get(`${this.serviceBaseUrl}/counts/statuses`, { headers });
  }

  getAppointmentsByProjects() {
    return this.http.get(`${this.serviceBaseUrl}/getappointmentsbyprojects`);
  }

  addProjects(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/add-projects`, payload);
  }

  getCurrency() {
    return this.http.get(`${this.serviceBaseUrl}/currency`);
  }

  getLocalites() {
    return this.http.get(`${env.baseURL}${env.apiURL}lead/localites`);
  }

  permanentdeleteLeads(ids: string[]) {
    let headers: any = {
      body: {
        ids: ids,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/delete`, headers);
  }

  getBulkOperation(pageNumber: number, pageSize: number, moduleType: string) {
    return this.http.get(
      `${this.serviceBaseUrl}/common-tracker?PageNumber=${pageNumber}&PageSize=${pageSize}&Type=${moduleType}`
    );
  }

  getUploadTypeNameList() {
    return this.http.get(`${this.serviceBaseUrl}/uploadtypename`);
  }

  getAuditHistory(id: string) {
    return this.httpClient.get(`${this.serviceBaseUrl}/histories/${id}`);
  }

  getAdditionalProperties() {
    return this.http.get(`${this.serviceBaseUrl}/additionalProperties/Keys`);
  }
  //AP - Additional Property
  getAPValues(key: string) {
    return this.http.get(`${this.serviceBaseUrl}/additionalProperties/values?key=${key}`);
  }

  navigateToLink(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/click-link`, payload);
  }

  getChannelPartnerList() {
    return this.http.get(`${this.serviceBaseUrl}/channelpartners`);
  }

  getChannelPartnerListAnonymous() {
    return this.http.get(`${this.serviceBaseUrl}/qr/channelpartners`);
  }

  getCampaignList() {
    return this.http.get(`${this.serviceBaseUrl}/campaigns`);
  }

  getCampaignListAnonymous() {
    return this.http.get(`${this.serviceBaseUrl}/qr/campaigns`);
  }

  getCountryBasedCity() {
    return this.http.get(`${this.serviceBaseUrl}/countrybasedcity`);
  }

  getNationality() {
    return this.http.get(`${this.serviceBaseUrl}/nationality`);
  }

  getLeadClusterName() {
    return this.http.get(`${this.serviceBaseUrl}/clustername`);
  }

  getLeadUnitName() {
    return this.http.get(`${this.serviceBaseUrl}/unitname`);
  }

  getLeadCountryCode() {
    return this.http.get(`${this.serviceBaseUrl}/countrycode`);
  }

  getLeadAltCountryCode() {
    return this.http.get(`${this.serviceBaseUrl}/altcountrycode`);
  }

  getParentLeadData(ids: string[]) {
    let params = new HttpParams();
    ids.forEach(id => {
      params = params.append('RootIds', id);
    });
    return this.http.get(`${this.serviceBaseUrl}/all/parentleads`, { params });
  }

  getLeadByIdWithArchive(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/parentlead/${id}`);
  }

  getLeadPostalCode() {
    return this.http.get(`${this.serviceBaseUrl}/postalcode`);
  }

  getLeadLandLine() {
    return this.http.get(`${this.serviceBaseUrl}/landline`);
  }
  getModuleWiseSearchProperties(moduleType: any) {
    return this.http.get(`${this.serviceBaseUrl}/properties/modulewise?ModuleType=${moduleType}`);
  }
}
