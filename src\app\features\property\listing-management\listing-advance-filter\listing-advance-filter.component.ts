import { Component, EventEmitter, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { select, Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { BHK_TYPE, BR_NO_ALL, COMPLETIONSTATUS, FACING, FINISHINGTYPE, FURNISH_STATUS, LISTING_POSSESSION_TYPE, NUMBER_5, PROP_DATE_TYPE, TRANSACTION_TYPE_LIST, UAE_EMIRATES } from 'src/app/app.constants';
import { EnquiryType, ListingFirstLevelFilter, ListingLevel, ListingVisibility, PropertyDateType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, changeCalendar, formatBudget, generateEnumList, getBRDisplayString, onlyN<PERSON><PERSON>, onlyNumbersWithDecimal, onPickerOpened, patchTimeZoneDate, setTimeZoneDate, validateAllFormFields } from 'src/app/core/utils/common.util';
import { FetchAllAmenities } from 'src/app/reducers/amenities-attributes/amenities-attributes.action';
import { getAllAmenities, getAmenitiesLoading } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchProjectList } from 'src/app/reducers/lead/lead.actions';
import { getProjectList, getProjectListIsLoading } from 'src/app/reducers/lead/lead.reducer';
import { FetchCommunities, FetchSubCommunities, UpdateListingPayload } from 'src/app/reducers/listing-site/listing-site.actions';
import { getCommunities, getListingFilters, getListingSiteLoaders, getSubCommunities } from 'src/app/reducers/listing-site/listing-site.reducer';
import { getAreaUnitIsLoading, getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchLocationList, FetchOwnerNames, FetchPropertyCurrency } from 'src/app/reducers/property/property.actions';
import { getLocationList, getLocationListIsLoading, getOwnerNames, getPropertyCurrencyList } from 'src/app/reducers/property/property.reducer';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'listing-advance-filter',
  templateUrl: './listing-advance-filter.component.html',
})
export class ListingAdvanceFilterComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('possessionFilter') possessionFilter: any;
  filtersForm: FormGroup;
  propertyTypeList = JSON.parse(localStorage.getItem('propertyType') || '[]');
  propertySubTypes: any[] = [];
  areaSizeUnits: any[] = [];
  dateTypeList = PROP_DATE_TYPE;
  enquiredFor = TRANSACTION_TYPE_LIST.slice(1, 3);
  enquiryType: string
  currentDate = new Date();
  filtersPayload: any;
  dateType: string;
  filterDate: any;
  possessionDate: any
  userData: any;
  listingPossessionDateFilterList = LISTING_POSSESSION_TYPE;
  defaultCurrency: string = 'INR';
  formatBudget = formatBudget;
  getBRDisplayString = getBRDisplayString;
  onPickerOpened = onPickerOpened;
  onlyNumbers = onlyNumbers;
  onlyNumbersWithDecimal = onlyNumbersWithDecimal;
  budgetValidation: boolean = true;
  allUserList: any;
  allUserListIsLoading: any;
  formFields: any = {
    DateType: [0],
    FromDate: [null],
    ToDate: [null],
    EnquiredFor: [null],
    PropertySubTypes: [null],
    MinPrice: [null],
    MaxPrice: [null],
    Currency: [null],
    FromPossesionDate: [null],
    ToPossesionDate: [null],
    PossesionType: [null],
    PropertyTitle: [null],
    UserIds: [null],
    ListingOnBehalf: [null],
    BasePropertyTypeId: [null],
    PropertyVisiblity: [0],
    FirstLevelFilter: [0],
    Locations: [null],
    Cities: [null],
    Communities: [null],
    SubCommunities: [null],
    States: [null],
    NoOfBHK: [null],
    BHKTypes: [null],
    OwnerNames: [null],
    Facing: [null],
    Amenities: [null],
    NoOfBathrooms: [null],
    FurnishStatuses: [null],
    NoOfLivingrooms: [null],
    NoOfBedrooms: [null],
    NoOfUtilites: [null],
    NoOfKitchens: [null],
    NoOfBalconies: [null],
    NoOfFloor: [null],
    MinPropertySize: [null],
    MaxPropertySize: [null],
    PropertySizeUnit: [null],
    MinCarpetArea: [null],
    MaxCarpetArea: [null],
    CarpetAreaUnit: [null],
    MinBuiltUpArea: [null],
    MaxBuiltUpArea: [null],
    BuiltUpAreaUnit: [null],
    MinSaleableArea: [null],
    MaxSaleableArea: [null],
    SaleableAreaUnit: [null],
    MinNetArea: [null],
    MaxNetArea: [null],
    NetAreaUnit: [null],
    CompletionStatus: [null],
    FinishingType: [null],
    UaeEmirates: [null],
    ListingLevel: [null],
    SerialNo: [null],
    MinLeadCount: [null],
    MaxLeadCount: [null],
    MinProspectCount: [null],
    MaxProspectCount: [null],
  };
  propertyCurrency: any;
  propertyCurrencyIsLoading: any;
  currency: any;
  globalSettingsData: any;
  canViewOwner: boolean;
  localityList: string[];
  cityList: string[];
  stateList: string[];
  isLocationListLoading: boolean;
  isProjectsListLoading: boolean;
  amenities: any[];
  isAmenitiesLoading: any;
  projectList: any;
  ownerNames: string[];
  numbers = NUMBER_5;
  bhkType = BHK_TYPE;
  noOfBhk = BR_NO_ALL;
  furnishStatus = FURNISH_STATUS;
  facing = FACING;
  listingStatus: any;
  offeringType: any[];
  saleableValidation: boolean = true;
  netValidation: boolean = true;
  buildUpAreaValidations: boolean = true;
  carpetAreaValidations: boolean = true;
  sizevalidations: boolean = true;
  completionStatusList: any = COMPLETIONSTATUS;
  uaeEmiratesList: any = UAE_EMIRATES;
  finishingTypeList: any = FINISHINGTYPE
  ListingLevel: any;
  communityList: string[];
  subCommunityList: string[];
  isCommunityLoading: boolean;
  isSubCommunityLoading: any;
  minBudgetValidation: boolean = true;
  maxBudgetValidation: boolean = true;
  minMaxLeadValidation: boolean = true;
  minMaxDataValidation: boolean = true;
  isAreaUnitsLoading: any;

  constructor(private _store: Store<AppState>,
    public modalRef: BsModalRef,
    private fb: FormBuilder,
  ) { }

  ngOnInit(): void {
    this.initializeForms()
    this.setupSubscriptions();
    this.setupLoadingStateSubscriptions();
    this.initializeDispatch();
    this.listingStatus = generateEnumList(ListingVisibility);
    this.offeringType = generateEnumList(ListingFirstLevelFilter);
    this.ListingLevel = generateEnumList(ListingLevel)
    this.propertyTypeList = this.propertyTypeList.filter(
      (property: any) => property.displayName !== 'Agricultural'
    );
    this.dateTypeList = this.dateTypeList?.filter((item: any) => item !== 'Possession Date');
  }

  initializeDispatch() {
    this._store.dispatch(new FetchPropertyCurrency());
    this._store.dispatch(new FetchLocationList());
    this._store.dispatch(new FetchOwnerNames());
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchCommunities());
    this._store.dispatch(new FetchSubCommunities());
    this._store.dispatch(new FetchAllAmenities());
  }

  setupSubscriptions(): void {
    const subscriptions = [
      {
        selector: getUserBasicDetails,
        handler: (data: any) => {
          this.userData = data;
          this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset);
        },
      },
      {
        selector: getListingFilters,
        handler: (filters: any) => {
          this.filtersPayload = filters;
          this.filtersForm.patchValue({
            ...filters,
            Currency: filters.Currency ?? this.defaultCurrency,
          });
          this.dateType = PropertyDateType[filters.DateType];
          this.filterDate = [
            patchTimeZoneDate(filters?.FromDate, this.userData?.timeZoneInfo?.baseUTcOffset),
            patchTimeZoneDate(filters?.ToDate, this.userData?.timeZoneInfo?.baseUTcOffset),
          ];
          this.possessionDate = [
            patchTimeZoneDate(filters?.FromPossesionDate, this.userData?.timeZoneInfo?.baseUTcOffset),
            patchTimeZoneDate(filters?.ToPossesionDate, this.userData?.timeZoneInfo?.baseUTcOffset),
          ];
        },
      },
      {
        selector: getGlobalSettingsAnonymous,
        handler: (data: any) => {
          this.defaultCurrency = data.countries?.[0]?.defaultCurrency || null;
          this.currency = data.countries?.[0]?.currencies?.map((cur: any) => cur.symbol) || null;
          this.globalSettingsData = data;
          this.filtersForm.patchValue({
            Currency: this.filtersPayload?.Currency ?? this.defaultCurrency
          })
        },
      },
      { selector: getPermissions, handler: this.handlePermissions.bind(this) },
      { selector: getLocationList, handler: this.handleLocationList.bind(this) },
      { selector: getCommunities, handler: this.handleCommunity.bind(this) },
      { selector: getSubCommunities, handler: this.handleSubCommunity.bind(this) },
      { selector: getUsersListForReassignment, handler: this.handleUsersList.bind(this) },
      { selector: getProjectList, handler: this.handleProjectList.bind(this) },
      { selector: getOwnerNames, handler: this.handleOwnerNames.bind(this) },
      {
        selector: getPropertyCurrencyList,
        handler: (data: any) => {
          this.propertyCurrency = data;
        },
      },
    ];

    subscriptions.forEach((sub) => {
      this._store
        .pipe(select(sub.selector), takeUntil(this.stopper))
        .subscribe(sub.handler);
    });

    this.propertySubTypes = [...this.propertyTypeList?.filter((type: any) => type.displayName !== 'Agricultural')?.map((type: any) => type.childTypes).flat()]
  }


  setupLoadingStateSubscriptions(): void {
    this._store.pipe(
      select(getLocationListIsLoading),
      takeUntil(this.stopper)
    ).subscribe(isLoading => this.isLocationListLoading = isLoading);

    this._store.pipe(
      select(getListingSiteLoaders),
      takeUntil(this.stopper)
    ).subscribe((data) => {
      this.isCommunityLoading = data?.communities
      this.isSubCommunityLoading = data?.subCommunities
    });

    this._store.pipe(
      select(getProjectListIsLoading),
      takeUntil(this.stopper)
    ).subscribe(isLoading => this.isProjectsListLoading = isLoading);
    this.handleAreaUnits()
    this.handleAmenities()
  }

  handlePermissions(permissions: any): void {
    const permissionsSet = new Set(permissions);
    this.canViewOwner = permissionsSet.has('Permissions.Properties.ViewOwnerInfo');
  }

  handleAreaUnits(): void {
    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });
    this._store
      .select(getAreaUnitIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isAreaUnitsLoading = isLoading
      });
  }


  handleCommunity(data: any): void {
    this.communityList = this.sortAndFilterList(data);
  }

  handleSubCommunity(data: any): void {
    this.subCommunityList = this.sortAndFilterList(data);
  }

  handleLocationList(data: any): void {
    if (!data) return;

    data.forEach((location: any) => {
      this.localityList = this.sortAndFilterList(location.locality);
      this.cityList = this.sortAndFilterList(location.city);
      this.stateList = this.sortAndFilterList(location.state);
    });
  }

  handleUsersList(data: any): void {
    this.allUserList = data?.map((user: any) => ({
      ...user,
      fullName: `${user.firstName} ${user.lastName}`
    }));
    this.allUserList = assignToSort(this.allUserList, '');
  }

  handleAmenities() {
    this._store
      .select(getAllAmenities)
      .pipe(takeUntil(this.stopper))
      .subscribe((amenities: any) => {
        const allAmenities = amenities?.flatMap((category: any) => category.amenities);
        this.amenities = allAmenities
        this.amenities?.sort((a: any, b: any) =>
          a.amenityDisplayName.localeCompare(b.amenityDisplayName)
        );
      });

    this._store
      .select(getAmenitiesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isAmenitiesLoading: boolean) => {
        this.isAmenitiesLoading = isAmenitiesLoading;
      });
  }

  handleProjectList(data: any): void {
    this.projectList = data?.slice().sort((a: any, b: any) => a.localeCompare(b)) || [];
  }

  handleOwnerNames(data: any): void {
    this.ownerNames = this.sortAndFilterList(data);
  }

  sortAndFilterList(list: any[]): string[] {
    return list
      ?.filter(item => item)
      ?.slice()
      ?.sort((a, b) => a.localeCompare(b)) || [];
  }

  getFormValue(controlName: string) {
    return this.filtersForm.get(controlName).value;
  }

  initializeForms() {
    this.filtersForm = this.fb.group(this.formFields);
  }

  budgetCheck(): void {
    const minBudget = this.getFormValue('MinPrice');
    const maxBudget = this.getFormValue('MaxPrice');

    if (minBudget && maxBudget && maxBudget < minBudget) {
      this.budgetValidation = false;
    } else {
      this.budgetValidation = true;
    }
  }

  sizevalidation(): void {
    const minArea = this.getFormValue('MinPropertySize');
    const maxArea = this.getFormValue('MaxPropertySize');

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.sizevalidations = false;
    } else {
      this.sizevalidations = true;
    }
  }

  validateBuildUpArea(): void {
    const minArea = this.getFormValue('MinBuiltUpArea');
    const maxArea = this.getFormValue('MaxBuiltUpArea');

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.buildUpAreaValidations = false;
    } else {
      this.buildUpAreaValidations = true;
    }
  }

  validateCarpetArea(): void {
    const minArea = this.getFormValue('MinCarpetArea');
    const maxArea = this.getFormValue('MaxCarpetArea');

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.carpetAreaValidations = false;
    } else {
      this.carpetAreaValidations = true;
    }
  }

  saleableAreaValidation(): void {
    const mBudget = this.getFormValue('MinSaleableArea');
    const maxBudget = this.getFormValue('MaxSaleableArea');

    if (mBudget && maxBudget && maxBudget < mBudget) {
      this.saleableValidation = false;
    } else {
      this.saleableValidation = true;
    }
  }

  netAreaValidation(): void {
    const minArea = this.getFormValue('MinNetArea');
    const maxArea = this.getFormValue('MaxNetArea');

    if (minArea && maxArea && maxArea < minArea) {
      this.netValidation = false;
    } else {
      this.netValidation = true;
    }
  }

  getEnquiryType(enquiry: string): number {
    return EnquiryType[enquiry as keyof typeof EnquiryType];
  }

  dateChange(): void {
    if (this.dateType && this.filterDate?.[0]) {
      const fromDate = setTimeZoneDate(this.filterDate?.[0], this.userData?.timeZoneInfo?.baseUTcOffset);
      const toDate = setTimeZoneDate(this.filterDate?.[1], this.userData?.timeZoneInfo?.baseUTcOffset);
      this.filtersForm.patchValue({
        DateType: PropertyDateType[this.dateType as keyof typeof PropertyDateType],
        FromDate: fromDate,
        ToDate: toDate,
      });
    }
  }

  onPossessionFilterChange(event: { possessionType: number | null; fromDate: string | null; toDate: string | null }): void {
    this.filtersForm.patchValue({
      PossesionType: event.possessionType,
      FromPossesionDate: event.fromDate,
      ToPossesionDate: event.toDate
    });
  }

  updatePropertySubType() {
    const baseTypeId = this.getFormValue('BasePropertyTypeId');

    this.propertySubTypes = baseTypeId
      ? this.propertyTypeList.find((type: any) => type.id === baseTypeId)?.childTypes || []
      : [...this.propertyTypeList.map((type: any) => type.childTypes).flat()];
  }


  onResetDateFilter() {
    if (this.getFormValue('DateType') !== null && this.getFormValue('FromDate')) {
      this.filtersForm.patchValue({
        DateType: null,
        FromDate: null,
        ToDate: null
      });
      this.filterDate = [null, null];
      this.dateType = PropertyDateType[0];
    }
  }

  applyAdvancedFilter() {
    if (!this.filtersForm.valid) {
      validateAllFormFields(this.filtersForm)
      return
    }
    if (!this.validateCustomDate()) {
      // Show validation error for custom date
      return;
    }
    if (!this.budgetValidation || !this.carpetAreaValidations || !this.buildUpAreaValidations || !this.saleableValidation || !this.sizevalidations || !this.netValidation) {
      return;
    }

    const formValues = this.filtersForm.value;
    this.filtersPayload = {
      ...this.filtersPayload,
      ...formValues,
      PageNumber: 1,
    };
    if (!this.getFormValue('MinPrice') && !this.getFormValue('MaxPrice')) {
      this.filtersPayload.Currency = null;
    }

    this._store.dispatch(new UpdateListingPayload(this.filtersPayload));
    this.modalRef.hide();
  }

  onClearAllFilters() {
    let resetObj: any = {};
    Object.keys(this.formFields)?.forEach((key: string) => {
      resetObj[key] = this.formFields[key][0];
    })
    this.filtersForm.patchValue(resetObj);
    this.filtersForm.get('Currency').setValue(this.defaultCurrency);
    this.filterDate = [null, null];
    this.possessionDate = null;
    this.dateType = PropertyDateType[0];
    if (this.possessionFilter) {
      this.possessionFilter.reset();
    }
  }

  minMaxLeadCheck(): void {
    this.minMaxLeadValidation = !(
      this.filtersForm.value.MinLeadCount &&
      this.filtersForm.value.MaxLeadCount &&
      this.filtersForm.value.MaxLeadCount < this.filtersForm.value.MinLeadCount
    );
  }

  minMaxDataCheck() {
    this.minMaxDataValidation = !(
      this.filtersForm.value.MinProspectCount &&
      this.filtersForm.value.MaxProspectCount &&
      this.filtersForm.value.MaxProspectCount < this.filtersForm.value.MinProspectCount
    );
  }

  validateCustomDate(): boolean {
    if (this.getFormValue('PossesionType') === 5) { // 5 is the enum value for 'Custom Date'
      const hasFromDate = this.getFormValue('FromPossesionDate') !== null;
      const hasToDate = this.getFormValue('ToPossesionDate') !== null;
      return hasFromDate && hasToDate;
    }
    return true;
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
