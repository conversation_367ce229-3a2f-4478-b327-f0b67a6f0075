<div class="position-relative">
  <div class="d-none tb-d-block tb-hamburger ph-mt-4" (click)="showLeftNavPopup = true">
    <span class="icon ic-hamburger-menu  ic-xs cursor-pointer mt-4 ph-mt-0"></span>
  </div>
  <div [ngClass]="showLeftNavPopup ? 'tb-left-nav' : 'tb-d-none'">
    <div class="left-nav-sticky module-navbar position-relative" [ngClass]="showLeftNav ? 'w-140' : 'w-40'">
      <span
        class="icon ic-cancel text-dark-gray ic-xxxs d-none tb-d-block position-absolute right-6 top-4 cursor-pointer"
        (click)="showLeftNavPopup = false"></span>
      <div class="bg-dark px-8 py-10 h-48 flex-center cursor-pointer" routerLink="leads">
        <ng-container *ngIf="showLeftNav else onlyLogo">
          <img [type]="'leadrat'" [appImage]="appImages?.appText" alt="App Text">
        </ng-container>
        <ng-template #onlyLogo>
          <img [type]="'leadrat'" [appImage]="appImages?.appLogo" alt="App Logo">
        </ng-template>
      </div>
      <div class="justify-between-col h-100-48 bg-dark">
        <ul *ngIf="!canView.length && permissionsIsLoading">
          <ng-container *ngFor="let skeleton of [1,2,3,4]">
            <ng-container *ngTemplateOutlet="skeletonLoader"></ng-container>
          </ng-container>
        </ul>
        <ul class="h-100-97 scrollbar scroll-hide" (scroll)="navVerticalScroll()">
          <a *ngIf="permissionsSet?.has('Permissions.Dashboard.View')" class="nav-item"
            [routerLink]="globalSettingsDetails.shouldHideDashBoard ? 'dashboard/metrics' : 'dashboard/stats'"
            routerLinkActive="active" (mouseenter)="onMouseEnter($event)"
            (click)="openLinkInNewTab($event, globalSettingsDetails.shouldHideDashBoard ? 'dashboard/metrics' : 'dashboard/stats'); showLeftNavPopup = false; onClick($event)">
            <div>
              <span class="ic-flower icon">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text" *ngIf="showLeftNav">{{ 'SIDEBAR.dashboard' | translate }}</a>
            </div>
            <p [ngClass]="showLeftNav ? 'd-none': 'on-hover-label'" [style.top.px]="heightFromTop">
              {{ 'SIDEBAR.dashboard' | translate }}</p>
          </a>
          <a class="nav-item" routerLink='global-config' routerLinkActive="active"
            (click)="openLinkInNewTab($event,'global-config'); showLeftNavPopup = false; onClick($event)"
            *ngIf="permissionsSet?.has('Permissions.GlobalSettings.View')" (mouseenter)="onMouseEnter($event)">
            <div>
              <span class="ic-hexagon icon">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span></span>
              <a class="nav-text" *ngIf="showLeftNav">{{ 'SIDEBAR.global-config' | translate }}</a>
            </div>
            <p [ngClass]="showLeftNav ? 'd-none': 'on-hover-label'" [style.top.px]="heightFromTop">{{
              'SIDEBAR.global-config' | translate }}</p>
          </a>
          <!-- <a *ngIf="permissionsSet?.has('Permissions.Leads.View') && globalSettingsDetails?.isWhatsAppDeepIntegration"
            class="nav-item" routerLink="whatsApp" routerLinkActive="active" (mouseenter)="onMouseEnter($event)"
            (click)="openLinkInNewTab($event, 'whatsApp'); showLeftNavPopup = false; onClick($event)">
            <div>
              <span class="icon ic-whatsapp">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text" *ngIf="showLeftNav">WhatsApp</a>
            </div>
            <p [ngClass]="showLeftNav ? 'd-none': 'on-hover-label'" [style.top.px]="heightFromTop">WhatsApp</p>
          </a> -->
          <a *ngIf="permissionsSet?.has('Permissions.Leads.View')" class="nav-item" routerLink="leads"
            routerLinkActive="active"
            (click)="openLinkInNewTab($event,'leads/manage-leads'); showLeftNavPopup = false; onClick($event)"
            (mouseenter)="onMouseEnter($event)">
            <div>
              <span class="icon ic-secondary-filter-solid">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text" *ngIf="showLeftNav">{{ 'SIDEBAR.leads' | translate }}</a>
            </div>
            <div class="sub-navbar" [ngClass]="showLeftNav ? 'left-140': 'left-40'" [style.top.px]="heightFromTop"
              *ngIf="isSubNavbarVisible">
              <ul *ngIf="permissionsSet?.has('Permissions.Leads.Create')">
                <a routerLink="leads/manage-leads" routerLinkActive="active" (click)="openLinkInNewTab($event)">{{
                  'SIDEBAR.manage-leads' | translate }}</a>
                <a routerLink="leads/add-lead" routerLinkActive="active"
                  (click)="openLinkInNewTab($event); stop($event); showLeftNavPopup = false">
                  {{'SIDEBAR.add' | translate}} {{'SIDEBAR.lead' | translate}}
                </a>
              </ul>
            </div>
          </a>
          <a *ngIf="permissionsSet?.has('Permissions.Prospects.View')" class="nav-item" routerLink="data"
            routerLinkActive="active"
            (click)="openLinkInNewTab($event,'data/manage-data'); showLeftNavPopup = false; onClick($event)"
            (mouseenter)="onMouseEnter($event)">
            <div>
              <span class="icon ic-address-card-solid">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text" *ngIf="showLeftNav">{{'CONTACT.data' | translate }}</a>
            </div>
            <div class="sub-navbar" [ngClass]="showLeftNav ? 'left-140': 'left-40'" [style.top.px]="heightFromTop"
              *ngIf="isSubNavbarVisible">
              <ul *ngIf="permissionsSet?.has('Permissions.Prospects.Create')">
                <a routerLink="data/manage-data" routerLinkActive="active"
                  (click)="openLinkInNewTab($event)">{{'SIDEBAR.manage-data' |
                  translate}}</a>
                <a routerLink="data/add-data" routerLinkActive="active"
                  (click)="openLinkInNewTab($event); stop($event); showLeftNavPopup = false">
                  Add Data</a>
              </ul>
            </div>
          </a>
          <a *ngIf="permissionsSet?.has('Permissions.Invoice.View')" class="nav-item" routerLinkActive="active"
            routerLink='invoice'
            (click)="openLinkInNewTab($event, 'invoice'); showLeftNavPopup = false; onClick($event)">
            <div>
              <span class="icon ic-dollar">
                <span class="pointer" *ngIf="!showLeftNav"></span>
              </span>
              <a class="nav-text" *ngIf="showLeftNav">Invoice</a>
            </div>
            <p [ngClass]="showLeftNav ? 'd-none': 'on-hover-label'">Invoice</p>
          </a>
          <a *ngIf="permissionsSet?.has('Permissions.Reports.ViewAllUsers') || permissionsSet?.has('Permissions.Reports.ViewReportees')"
            class="nav-item" routerLinkActive="active"
            (click)="openLinkInNewTab($event,'reports/status-report'); showLeftNavPopup = false; onClick($event)"
            (mouseenter)="onMouseEnter($event)">
            <div routerLink="reports/status-report" (click)="openLinkInNewTab($event)" class="w-100">
              <span class="icon ic-chart-pie">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text" *ngIf="showLeftNav">{{ 'SIDEBAR.reports' | translate }}</a>
            </div>
            <div class="sub-navbar" [ngClass]="showLeftNav ? 'left-140': 'left-40'" [style.top.px]="heightFromTop"
              *ngIf="isSubNavbarVisible">
              <ul>
                <a routerLinkActive="active" routerLink="reports/merge-activity-report"
                  (click)="openLinkInNewTab($event)">
                  Activity </a>
                <a routerLinkActive="active" routerLink="reports/agency-report" (click)="openLinkInNewTab($event)">
                  {{ 'REPORTS.agency' | translate }}</a>
                <a routerLinkActive="active" class="flex-between">Data <span
                    class="icon ic-chevron-right ic-xxs "></span>
                  <div class="sup-sub-nav left-150 ip-left-100px mt-100">
                    <ul>
                      <a routerLinkActive="active" routerLink="reports/data-call-report"
                        (click)="openLinkInNewTab($event)">
                        Call</a>
                      <a routerLinkActive="active" routerLink="reports/data-activity-report"
                        (click)="openLinkInNewTab($event)">
                        Activity </a>
                      <a routerLinkActive="active" routerLink="reports/data-status-report"
                        (click)="openLinkInNewTab($event)">
                        {{'GLOBAL.status' | translate}}</a>
                      <a routerLinkActive="active" routerLink="reports/data-project-report"
                        (click)="openLinkInNewTab($event)">
                        {{ 'SIDEBAR.project' | translate }} </a>
                      <a routerLinkActive="active" routerLink="reports/data-source-report"
                        (click)="openLinkInNewTab($event)">
                        Source </a>
                      <a routerLinkActive="active" routerLink="reports/data-subsource-report"
                        (click)="openLinkInNewTab($event)">
                        Sub-Source </a>
                    </ul>
                  </div>
                </a>
                <a routerLinkActive="active" class="flex-between">Leads <span
                    class="icon ic-chevron-right ic-xxs "></span>
                  <div class="sup-sub-nav left-150 ip-left-100px mt-50">
                    <ul>
                      <a routerLinkActive="active" routerLink="reports/call-report" (click)="openLinkInNewTab($event)">
                        Call</a>
                      <a routerLinkActive="active" routerLink="reports/activity-report"
                        (click)="openLinkInNewTab($event)">
                        {{'GLOBAL.activity' | translate}} </a>
                      <a routerLinkActive="active" routerLink="reports/status-report"
                        (click)="openLinkInNewTab($event)">
                        {{'GLOBAL.status' | translate}}</a>
                      <a routerLinkActive="active" routerLink="reports/project-report"
                        (click)="openLinkInNewTab($event)">
                        {{ 'SIDEBAR.project' | translate }} </a>
                      <a routerLinkActive="active" routerLink="reports/source-report"
                        (click)="openLinkInNewTab($event)">
                        Source </a>
                      <a routerLinkActive="active" routerLink="reports/sub-source-report"
                        (click)="openLinkInNewTab($event)">
                        Sub-Source </a>
                      <a routerLinkActive="active" routerLink="reports/sub-status-report"
                        (click)="openLinkInNewTab($event)">
                        Sub-Status</a>
                      <!-- <a routerLinkActive="active" routerLink="reports/city-report" (click)="openLinkInNewTab($event)">
                        City </a> -->
                      <a routerLinkActive="active" routerLink="reports/user-source-report"
                        (click)="openLinkInNewTab($event)">
                        User vs Source </a>
                      <a routerLinkActive="active" routerLink="reports/user-subsource-report"
                        (click)="openLinkInNewTab($event)">
                        User vs Sub-Source </a>
                    </ul>
                  </div>
                </a>
                <a routerLinkActive="active" routerLink="reports/project-substatus" class="text-nowrap"
                  (click)="openLinkInNewTab($event)">
                  <span class="ip-text-truncate"> Project vs Sub-Status </span> </a>
                <a routerLinkActive="active" routerLink="reports/received-date-source"
                  (click)="openLinkInNewTab($event)" class="text-nowrap">
                  <span class="ip-text-truncate"> Received Date vs Source</span></a>
                <a routerLinkActive="active" routerLink="reports/substatus-subsource" class="text-nowrap"
                  (click)="openLinkInNewTab($event)">
                  <span class="ip-text-truncate"> Sub-Status vs Sub-Source </span> </a>
                <a routerLinkActive="active" routerLink="reports/visit-meeting-report"
                  (click)="openLinkInNewTab($event)">
                  {{ 'REPORTS.visit-meeting' | translate }}</a>
              </ul>
            </div>
          </a>
          <a *ngIf="permissionsSet?.has('Permissions.Projects.View')" class="nav-item" routerLinkActive="active"
            routerLink="projects"
            (click)="openLinkInNewTab($event,'projects/manage-projects'); showLeftNavPopup = false; onClick($event)"
            (mouseenter)="onMouseEnter($event)">
            <div>
              <span class="icon ic-buliding-secondary-solid">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text " *ngIf="showLeftNav">{{ 'PROJECTS.projects' | translate }}</a>
            </div>
            <div class="sub-navbar" [ngClass]="{'d-none': scrolling, 'left-140': showLeftNav, 'left-40': !showLeftNav}"
              *ngIf="isSubNavbarVisible" [style.top.px]="heightFromTop">
              <ul *ngIf="permissionsSet?.has('Permissions.Projects.Create')">
                <a routerLink="projects/manage-projects" routerLinkActive="active"
                  (click)="openLinkInNewTab($event)">{{'SIDEBAR.manage-projects'
                  | translate}}</a>
                <a routerLink="projects/add-project" routerLinkActive="active"
                  (click)="openLinkInNewTab($event); stop($event); showLeftNavPopup = false">{{'SIDEBAR.add' |
                  translate}}
                  {{'SIDEBAR.project' | translate}}</a>
              </ul>
            </div>
          </a>
          <a *ngIf="permissionsSet?.has('Permissions.Properties.View') || permissionsSet?.has('Permissions.Properties.ViewAssigned')"
            class="nav-item" [routerLink]="isViewlisting ? 'properties/manage-listing' : 'properties'"
            routerLinkActive="active"
            (click)="openLinkInNewTab($event, isViewlisting ? 'properties/manage-listing' : 'properties/manage-properties'); showLeftNavPopup = false; onClick($event)"
            (mouseenter)="onMouseEnter($event)">
            <div>
              <span class="icon ic-house-solid">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text " *ngIf="showLeftNav">
                {{ 'SIDEBAR.properties' | translate}}</a>
            </div>
            <div class="sub-navbar" [ngClass]="{'d-none': scrolling, 'left-140': showLeftNav, 'left-40': !showLeftNav}"
              *ngIf="isSubNavbarVisible" [style.top.px]="heightFromTop">
              <ul>
                <a *ngIf="permissionsSet?.has('Permissions.Properties.View') && !isViewlisting"
                  routerLink="properties/manage-properties" routerLinkActive="active"
                  (click)="openLinkInNewTab($event)">
                  {{ 'SIDEBAR.manage-properties' | translate}}</a>
                <a *ngIf="permissionsSet?.has('Permissions.Properties.View') && isViewlisting" routerLinkActive="active"
                  routerLink="properties/manage-listing" (click)="openLinkInNewTab($event); stop($event);">
                  Listing Management</a>
                <a *ngIf="permissionsSet?.has('Permissions.Properties.Create')"
                  [routerLink]="isViewlisting ?  'properties/add-listing' : 'properties/add-property'"
                  routerLinkActive="active" (click)="openLinkInNewTab($event); stop($event); showLeftNavPopup = false">
                  {{ 'SIDEBAR.add' | translate }} {{ 'LABEL.property' | translate}}
                </a>
                <a *ngIf="isViewlisting" [routerLink]="'properties/manage-reference-id'" routerLinkActive="active"
                  (click)="openLinkInNewTab($event); stop($event); showLeftNavPopup = false">
                  Ref. ID Management
                </a>
              </ul>
            </div>
          </a>
          <a *ngIf="permissionsSet?.has('Permissions.Attendance.ViewAllUsers') || permissionsSet?.has('Permissions.Attendance.ViewReportees')"
            class="nav-item" routerLinkActive="active"
            (click)="openLinkInNewTab($event,'attendance'); showLeftNavPopup = false; onClick($event)"
            routerLink="attendance" (mouseenter)="onMouseEnter($event)">
            <div class="w-100">
              <span class="icon ic-calendar-tick">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text " *ngIf="showLeftNav">{{ 'SIDEBAR.attendance' | translate }}</a>
            </div>
            <p [ngClass]="showLeftNav ? 'd-none': 'on-hover-label'" [style.top.px]="heightFromTop">{{
              'SIDEBAR.attendance' | translate }}</p>
          </a>
          <a *ngIf="permissionsSet?.has('Permissions.Todos.View')" class="nav-item" routerLink="task"
            routerLinkActive="active"
            (click)="openLinkInNewTab($event,'task/manage-task'); showLeftNavPopup = false; onClick($event)"
            (mouseenter)="onMouseEnter($event)">
            <div>
              <span class="icon ic-notepad-solid">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text " *ngIf="showLeftNav">
                {{ 'SIDEBAR.tasks' | translate }}</a>
            </div>
            <div class="sub-navbar" [ngClass]="showLeftNav ? 'left-140': 'left-40'" [style.top.px]="heightFromTop"
              *ngIf="isSubNavbarVisible">
              <ul *ngIf="permissionsSet?.has('Permissions.Todos.Create')">
                <a routerLink="task/manage-task" routerLinkActive="active" (click)="openLinkInNewTab($event)">{{
                  'SIDEBAR.manage' | translate }} {{
                  'SIDEBAR.tasks' | translate }}
                </a>
                <a (click)="navigateToAddTask()">{{ 'TASK.add-task' | translate }}</a>
              </ul>
            </div>
          </a>
          <a *ngIf="permissionsSet?.has('Permissions.Users.View') || permissionsSet?.has('Permissions.Roles.View') || permissionsSet?.has('Permissions.Teams.View')"
            class="nav-item" routerLinkActive="active"
            (click)="openLinkInNewTab($event,'teams/manage-user'); showLeftNavPopup = false; onClick($event)"
            (mouseenter)="onMouseEnter($event)">
            <div class="w-100"
              [routerLink]="permissionsSet?.has('Permissions.Users.View') ? 'teams' : permissionsSet?.has('Permissions.Teams.View') ? 'teams/manage-team' : 'teams/manage-role'">
              <span class="icon ic-three-person-solid">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text " *ngIf="showLeftNav">{{ 'SIDEBAR.team' | translate }}</a>
            </div>
            <div class="sub-navbar" [ngClass]="showLeftNav ? 'left-140': 'left-40'" [style.top.px]="heightFromTop"
              *ngIf="isSubNavbarVisible">
              <ul>
                <a *ngIf="permissionsSet?.has('Permissions.Users.View')" routerLinkActive="active"
                  routerLink="teams/manage-user" (click)="openLinkInNewTab($event)">
                  {{ 'SIDEBAR.manage-users' | translate }}</a>
                <a *ngIf="permissionsSet?.has('Permissions.Teams.View')" routerLinkActive="active"
                  routerLink="teams/manage-team" (click)="openLinkInNewTab($event)">
                  {{ 'SIDEBAR.manage-team' | translate }}s</a>
                <a *ngIf="permissionsSet?.has('Permissions.Roles.View')" routerLinkActive="active"
                  routerLink="teams/manage-role" (click)="openLinkInNewTab($event)">
                  {{ 'USER_MANAGEMENT.role-permissions' | translate }}</a>
                <!-- <a routerLinkActive="active" routerLink="teams/employee-tracking">
                  {{ 'USER.employee-tracking' | translate }}</a>
                <a routerLinkActive="active" routerLink="teams/tracking">
                  {{ 'USER.tracking' | translate }}</a> -->
              </ul>
            </div>
          </a>
          <a *ngIf="permissionsSet?.has('Permissions.OrgProfile.View')" class="nav-item" routerLinkActive="active"
            routerLink='profile'
            (click)="openLinkInNewTab($event,'profile/profile-dashboard/basic-details'); showLeftNavPopup = false; onClick($event)"
            (mouseenter)="onMouseEnter($event)">
            <div>
              <span class="icon ic-corporation">
                <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
              </span>
              <a class="nav-text" *ngIf="showLeftNav">{{ 'PROFILE.org-profile' | translate }}</a>
            </div>
            <p [ngClass]="showLeftNav ? 'd-none': 'on-hover-label'" [style.top.px]="heightFromTop">
              {{'PROFILE.org-profile' | translate}}</p>
          </a>
          <ng-container *ngIf="subDomain === 'lrbnewqa'">
            <ng-container *ngFor="let app of productsList">
              <a class="nav-item" [class.active]="selectedAppId === app.id && router.url === '/this-applications'"
                [routerLink]="'/this-applications'"
                (click)="onAppsClick($event, app); showLeftNavPopup = false;  onClick($event)"
                (mouseenter)="onMouseEnter($event)">
                <div class="w-100">
                  <span class="icon" [ngClass]="app?.icon || 'ic-setting-connections'">
                    <span class="pointer" *ngIf="!showLeftNav" [style.top.px]="heightFromTop"></span>
                  </span>
                  <a class="nav-text" *ngIf="showLeftNav">
                    <span class="overflow-hidden break-all text-truncate-1">{{app?.name}}</span></a>
                </div>
                <p [ngClass]="showLeftNav ? 'd-none': 'on-hover-label'" [style.top.px]="heightFromTop">
                  {{app?.name}}
                </p>
              </a>
            </ng-container>
          </ng-container>
        </ul>
        <div>
          <!-- <ul *ngIf="isProdEnv && !permissionsIsLoading" class="border-bottom-dark-600">
            <a class="nav-item bg-white" routerLink='/external/refer-earn'
              (click)="openLinkInNewTab($event,'/external/refer-earn'); showLeftNavPopup = false; onClick($event)"
              (mouseenter)="onMouseEnter($event)">
              <div>
                <span>
                  <img src="../../../assets/images/refer-earn/refer-earn-icon.svg" alt="" height="30"
                    *ngIf="!showLeftNav">
                  <span class="pointer" [style.top.px]="heightFromTop"></span></span>
                <a class="nav-text " *ngIf="showLeftNav">
                  <img src="../../../assets/images/refer-earn/refer-earn-full.svg" alt="" width="100" height="35">
                </a>
              </div>
              <p [ngClass]="showLeftNav ? 'd-none': 'on-hover-label'" [style.top.px]="heightFromTop">Refer & Earn</p>
            </a>
          </ul> -->
          <ul *ngIf="isProdEnv && !permissionsIsLoading">
            <a class="nav-item bg-white" routerLink='engage-to' routerLinkActive="active"
              (click)="openLinkInNewTab($event,'engage-to'); showLeftNavPopup = false; onClick($event)"
              *ngIf="permissionsSet?.has('Permissions.GlobalSettings.View')" (mouseenter)="onMouseEnter($event)">
              <div>
                <span class="ic-engageto icon ic-black" *ngIf="!showLeftNav">
                  <span class="pointer" [style.top.px]="heightFromTop"></span></span>
                <a class="nav-text " *ngIf="showLeftNav">
                  <img src="../../../assets/images/engage-to-logo.svg" alt="" height="33">
                </a>
              </div>
              <p [ngClass]="showLeftNav ? 'd-none': 'on-hover-label'" [style.top.px]="heightFromTop">Engage to</p>
            </a>
          </ul>
          <div class="flex-between p-12 bg-black-100">
            <div class="text-black-400 flex-center" [ngClass]="newUpdateAvailable ? 'cursor-pointer' : 'mx-10 pe-none'"
              *ngIf="showLeftNav" [title]="newUpdateAvailable ? 'Upgrade available - click to apply!' : ''"
              (click)="updateVersion()">
              <ng-lottie [options]='online' width="30px" *ngIf="newUpdateAvailable"></ng-lottie>
              v{{versionNo}}
            </div>
            <span id="sidebarCollapse" class="icon ic-xs cursor-pointer ic-light-gray icon-hover-white"
              (click)="toggleLeftNav()" [ngClass]="showLeftNav ? 'ic-double-left' : 'ic-double-right'"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #skeletonLoader>
  <a class="px-4 py-8">
    <div>
      <span class="icon icon__skeleton shimmer">
      </span>
      <span class="nav-text nav-text__skeleton shimmer">
      </span>
    </div>
  </a>
</ng-template>