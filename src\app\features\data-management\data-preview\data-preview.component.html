<div routerLink='/data/manage-data' *ngIf="canViewData" [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
    class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<!-- dont remove this code -->
<!-- <div class="position-absolute top-13 z-index-1021 cursor-pointer right-80 d-flex">
    <div class="align-center border px-10 py-6 br-2 mr-16"><span
            class="icon ic-x-xs ic-black-200 ic-triangle-down rotate-90"></span>
        <h5 class="ip-d-none ml-6">Previous Contact</h5>
    </div>
    <div class="align-center border px-10 py-6 br-2"><span
            class="icon ic-x-xs ic-black-200 ic-triangle-down rotate-270"></span>
        <h5 class="ip-d-none ml-6">Next Contact</h5>
    </div>
</div> -->
<div id="swipeArea">
    <div class="m-20 d-flex tb-flex-col position-relative h-100-130" *ngIf="canViewData && !isDataLoading">
        <div class="d-flex ip-flex-col w-70 tb-w-100">
            <div class="border w-45 ip-w-100 bg-white br-4 mr-20">
                <div class="d-flex p-12">
                    <div class="dot dot-lg-xxl bg-red-450">
                        <h3 class="text-white">{{getAbbreviation(data?.name)}}</h3>
                    </div>
                    <div class="ml-10">
                        <h4 class="fw-600 text-truncate-1 break-all">{{data?.name}}</h4>
                        <span class="status-label-badge mt-4"
                            [ngStyle]="{'color': data?.status?.color || '#4B4B4B', 'background-color': hexToRgba(data?.status?.color || '#4B4B4B', 0.08)}">
                            <span class="dot dot-xs mr-6"
                                [ngStyle]="{'background-color':  data?.status?.color || '#4B4B4B'}"></span>
                            {{data?.status?.displayName}}
                        </span>
                        <div [ngClass]="{'pe-none grid-blur': (data?.isArchived || data?.isConvertedToLead)}">
                            <div class="mt-10 d-flex">
                                <div *ngIf="canEditData"
                                    (click)="openEditData();trackingService.trackFeature('Web.Data.Button.DataPreviewEdit.Click')"
                                    title="Edit" class="bg-light-pearl br-10 cursor-pointer w-30px h-30px flex-center">
                                    <span class="icon ic-pen ic-black-200 ic-sm"></span>
                                </div>
                                <div (click)="checkToCall($event, virtualNo, askBefore, contactAdmin, chooseToCallType, data?.id);trackingService.trackFeature('Web.Data.Button.DataPreviewCall.Click')"
                                    title="Call"
                                    class="bg-light-pearl br-10 cursor-pointer w-30px h-30px flex-center ml-10">
                                    <span class="icon ic-call-ring ic-black-200 ic-sm"></span>
                                </div>
                                <!-- dont remove this code -->
                                <!-- <div title="sms" class="bg-light-pearl br-10 cursor-pointer w-30px h-30px flex-center ml-10">
                                                <span class="icon ic-message-lines ic-black-200 ic-sm"></span>
                                            </div> -->
                                <div title="Whatsapp"
                                    (click)="openTemplateModal($event, 'WhatsApp');trackingService.trackFeature('Web.Data.Button.DataPreviewWhatsapp.Click')"
                                    class="bg-light-pearl br-10 cursor-pointer w-30px h-30px flex-center ml-10">
                                    <span class="icon ic-whatsapp ic-black-200 ic-sm"></span>
                                </div>
                            </div>
                            <div class="mt-10 br-4 h-30px w-130 br-6 btn btn-linear-green align-center"
                                *ngIf="canConvertToLead" (click)="openConvertedModal(confirmationPopup)">
                                <span class="mr-6 text-nowrap"> Convert to Lead</span>
                                <span class="icon ic-x-xs ic-triangle-up rotate-90"></span>
                            </div>
                            <form [formGroup]="contactForm" class="d-none">
                                <ngx-mat-intl-tel-input #contactNoInput formControlName="contactNo"
                                    class="no-validation contactNoInput" id="contactNoInput">
                                </ngx-mat-intl-tel-input>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="border-top"></div>
                <div class="cursor-pointer align-center ml-20 mt-16" (click)="showContact = !showContact">
                    <span class="ic-triangle-down icon ic-coal ic-xxxs mr-12"
                        [ngClass]="{ 'rotate-270' : !showContact}"></span>
                    <h5 class="fw-700 text-black-200">About this Contact</h5>
                </div>
                <div class="border-bottom-slate-20 mt-10 ml-40 mr-20"></div>
                <div class="scrollbar" [ngClass]="canConvertToLead ? 'h-100-333' : 'h-100-293'">
                    <div class="ml-24 mr-10" *ngIf="showContact">
                        <div class="mt-10 align-center fw-semi-bold">
                            <span class="ic-call-ring ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4 text-lowercase">{{'USER.phone-number' |
                                translate}}:</span>
                            <h6>{{data?.contactNo? data?.contactNo: '--'}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold">
                            <span class="ic-Call ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4 text-lowercase">{{'GLOBAL.alternate' | translate}}
                                {{'GLOBAL.number' |
                                translate}}:</span>
                            <h6>{{data?.alternateContactNo? data?.alternateContactNo: '--'}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold">
                            <span class="ic-Call ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4 text-lowercase">Landline Number:</span>
                            <h6>{{data?.landLine? data?.landLine: '--'}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold">
                            <span class="ic-envelope ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4 text-lowercase text-nowrap">{{ 'USER.email-id' |
                                translate
                                }}:</span>
                            <h6 class="text-truncate-1 break-all">{{data?.email? data?.email: '--'}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold">
                            <span class="ic-circle-user-secondary ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4 text-nowrap">assigned to:</span>
                            <h6 class="text-truncate-1 break-all">{{getName(data?.assignTo)}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold">
                            <div class="w-50">
                                <span class="text-sm text-dark-gray">created by</span>
                                <h6 class="text-truncate-1 break-all mr-4">{{getName(data?.createdBy)}}</h6>
                            </div>
                            <div class="w-50">
                                <span class="text-sm text-dark-gray mr-4">created on</span>
                                <h6 class="text-truncate-1 break-all">{{data?.createdOn?
                                    getTimeZoneDate(data?.createdOn, userBasicDetails?.timeZoneInfo?.baseUTcOffset,
                                    'dayMonthYearText') :"--"}}</h6>
                            </div>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold">
                            <div class="w-50">
                                <span class="text-sm text-dark-gray">modified by</span>
                                <h6 class="text-truncate-1 break-all mr-4">{{getName(data?.lastModifiedBy)}}</h6>
                            </div>
                            <div class="w-50">
                                <span class="text-sm text-dark-gray mr-4">modified on</span>
                                <h6 class="text-truncate-1 break-all">{{data?.lastModifiedOn?
                                    getTimeZoneDate(data?.lastModifiedOn,
                                    userBasicDetails?.timeZoneInfo?.baseUTcOffset, 'dayMonthYearText') :"--"}}</h6>
                            </div>
                        </div>
                    </div>
                    <div class="cursor-pointer align-center ml-20 mt-30" (click)="showInfo = !showInfo">
                        <span class="ic-triangle-down icon ic-coal ic-xxxs mr-12"
                            [ngClass]="{ 'rotate-270' : !showInfo}"></span>
                        <h5 class="fw-700 text-black-200">Enquiry Info</h5>
                    </div>
                    <div class="border-bottom-slate-20 mt-10 ml-40 mr-20"></div>
                    <div class="ml-24 mr-10" *ngIf="showInfo">
                        <div class="mt-10 align-center fw-semi-bold">
                            <span class="ic-refine ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4">source:</span>
                            <h6 class="text-truncate-1 break-all">{{data?.enquiry?.prospectSource?.displayName?
                                data?.enquiry?.prospectSource?.displayName:
                                '--'}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold">
                            <span class="ic-circle-arrow ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4">purpose:</span>
                            <h6 class="text-truncate-1 break-all">{{ PurposeType[data?.enquiry?.purpose] ?
                                PurposeType[data?.enquiry?.purpose] :
                                '--'}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold" *ngIf="data?.enquiry?.subSource">
                            <span class="ic-refine ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4">sub source:</span>
                            <h6 class="text-truncate-1 break-all">{{data?.enquiry?.subSource? data?.enquiry?.subSource:
                                '--'}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold">
                            <span class="ic-re-share ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4">enquired for:</span>
                            <h6 class="text-truncate-1 break-all">{{enquiryTypes}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold"
                            [ngClass]="{'blinking pe-none': isAreaSizeUnitsLoading}">
                            <span class="ic-diamond ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4">carpet area:</span>
                            <h6 class="text-truncate-1 break-all">{{data?.enquiry?.carpetArea?
                                data?.enquiry?.carpetArea:'--'}}
                                {{areaSizeUnits?.[data?.enquiry?.carpetAreaUnitId] || ''}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold"
                            [ngClass]="{'blinking pe-none': isAreaSizeUnitsLoading}">
                            <span class="ic-diamond ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4">builtUp area:</span>
                            <h6 class="text-truncate-1 break-all">{{data?.enquiry?.builtUpArea?
                                data?.enquiry?.builtUpArea:'--'}}
                                {{areaSizeUnits?.[data?.enquiry?.builtUpAreaUnitId] || ''}}</h6>
                        </div>
                        <div class="mt-10 align-center fw-semi-bold"
                            [ngClass]="{'blinking pe-none': isAreaSizeUnitsLoading}">
                            <span class="ic-diamond ic-light-gray ic-xxs mr-12"></span>
                            <span class="text-sm text-dark-gray mr-4">saleable area:</span>
                            <h6 class="text-truncate-1 break-all">{{data?.enquiry?.saleableArea?
                                data?.enquiry?.saleableArea:'--'}}
                                {{areaSizeUnits?.[data?.enquiry?.saleableAreaUnitId] || ''}}</h6>
                        </div>
                        <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                            <div class="mt-10 align-center fw-semi-bold"
                                [ngClass]="{'blinking pe-none': isAreaSizeUnitsLoading}">
                                <span class="ic-diamond ic-light-gray ic-xxs mr-12"></span>
                                <span class="text-sm text-dark-gray mr-4">property area:</span>
                                <h6 class="text-truncate-1 break-all">{{data?.enquiry?.propertyArea?
                                    data?.enquiry?.propertyArea:'--'}}
                                    {{areaSizeUnits?.[data?.enquiry?.propertyAreaUnitId] || ''}}</h6>
                            </div>
                            <div class="mt-10 align-center fw-semi-bold"
                                [ngClass]="{'blinking pe-none': isAreaSizeUnitsLoading}">
                                <span class="ic-diamond ic-light-gray ic-xxs mr-12"></span>
                                <span class="text-sm text-dark-gray mr-4">net area:</span>
                                <h6 class="text-truncate-1 break-all">{{data?.enquiry?.netArea?
                                    data?.enquiry?.netArea:'--'}}
                                    {{areaSizeUnits?.[data?.enquiry?.netAreaUnitId] || ''}}</h6>
                            </div>
                            <div class="mt-10 align-center fw-semi-bold">
                                <span class="ic-cube ic-light-gray ic-xxs mr-12"></span>
                                <span class="text-sm text-dark-gray mr-4">unit number/name:</span>
                                <h6 class="text-truncate-1 break-all">{{data?.enquiry?.unitName?
                                    data?.enquiry?.unitName:'--'}}
                                </h6>
                            </div>
                            <div class="mt-10 align-center fw-semi-bold">
                                <span class="ic-molecules ic-light-gray ic-xxs mr-12"></span>
                                <span class="text-sm text-dark-gray mr-4">cluster name:</span>
                                <h6 class="text-truncate-1 break-all">{{data?.enquiry?.clusterName?
                                    data?.enquiry?.clusterName:'--'}}
                                </h6>
                            </div>
                        </ng-container>
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-home ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.properties?.length?'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">properties:</span>
                                <div class="d-flex flex-wrap" *ngIf="data?.properties?.length">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngFor="let property of data?.properties">{{property?.title}}</h6>
                                </div>
                                <div *ngIf="!data?.properties?.length">--</div>
                            </div>
                        </div>
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-corporation ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.projects?.length?'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">projects:</span>
                                <div class="d-flex flex-wrap" *ngIf="data?.projects?.length">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngFor="let project of data?.projects">{{ project?.name}}</h6>
                                </div>
                                <div *ngIf="!data?.projects?.length">--</div>
                            </div>
                        </div>
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-apartment ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.enquiry?.propertyTypes?.length?'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">property type:</span>
                                <div class="d-flex flex-wrap">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngIf="data?.enquiry?.propertyTypes?.[0]?.displayName">
                                        {{data?.enquiry?.propertyTypes?.[0]?.displayName}}</h6>
                                    <h6 *ngIf="!data?.enquiry?.propertyTypes?.[0]?.displayName">--</h6>
                                </div>
                            </div>
                        </div>
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-rocket ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.enquiry?.propertyTypes?.length ? 'flex-col' : 'd-flex'">
                                <span class="text-sm text-dark-gray mr-4">Property Sub-Type:</span>
                                <div class="align-center flex-wrap">
                                    <ng-container *ngIf="data?.enquiry?.propertyTypes?.length; else noSubtype">
                                        <ng-container *ngFor="let type of data?.enquiry?.propertyTypes">
                                            <h6 *ngIf="type?.childType?.displayName"
                                                class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12">
                                                {{ type.childType.displayName }}
                                            </h6>
                                        </ng-container>
                                    </ng-container>
                                    <ng-template #noSubtype>
                                        <h6>--</h6>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                        <ng-container
                            *ngIf="data?.enquiry?.propertyTypes?.[0]?.displayName == 'Residential' && hasNonPlotPropertyTypes">
                            <div *ngIf="!globalSettingsDetails?.isCustomLeadFormEnabled"
                                class="mt-10 d-flex fw-semi-bold">
                                <span class="ic-rocket ic-light-gray ic-xxs mr-12"></span>
                                <div [ngClass]="bhkNo && bhkNo?.length > 0 ? 'flex-col':'d-flex'">
                                    <span class="text-sm text-dark-gray mr-4">BHK</span>
                                    <div class="d-flex text-wrap">
                                        <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                            *ngIf="bhkNo && bhkNo?.length > 0">
                                            {{bhkNo && bhkNo?.length > 0 ? bhkNo : '--'}}</h6>
                                        <h6 *ngIf="!bhkNo && !bhkNo?.length > 0">--</h6>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-10 d-flex fw-semi-bold"
                                *ngIf="!globalSettingsDetails?.isCustomLeadFormEnabled">
                                <span class="ic-rocket ic-light-gray ic-xxs mr-12"></span>
                                <div [ngClass]="bhkTypes ? 'flex-col':'d-flex'">
                                    <span class="text-sm text-dark-gray mr-4">{{ "PROPERTY.bhk" | translate }} {{
                                        "LABEL.type" |
                                        translate }}</span>
                                    <div class="d-flex text-wrap">
                                        <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12" *ngIf="bhkTypes">
                                            {{bhkTypes}}</h6>
                                        <h6 *ngIf="!bhkTypes">--</h6>

                                    </div>
                                </div>
                            </div>
                            <div class="mt-10 d-flex fw-semi-bold"
                                *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                                <span class="ic-bed-secondary-solid ic-light-gray ic-xxs mr-12"></span>
                                <div [ngClass]="beds?.length ? 'flex-col':'d-flex'">
                                    <span class="text-sm text-dark-gray mr-4">Beds</span>
                                    <div class="d-flex text-wrap">
                                        <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                            *ngIf="beds?.length">
                                            {{beds?.length ? beds : '--'}}</h6>
                                        <h6 *ngIf="!beds?.length">--</h6>

                                    </div>
                                </div>
                            </div>
                            <div class="mt-10 d-flex fw-semi-bold"
                                *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                                <span class="ic-bath-light ic-light-gray ic-xxs mr-12"></span>
                                <div
                                    [ngClass]="data?.enquiry?.baths && data?.enquiry?.baths?.length > 0 ? 'flex-col':'d-flex'">
                                    <span class="text-sm text-dark-gray mr-4">Baths</span>
                                    <div class="d-flex text-wrap">
                                        <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                            *ngIf="data?.enquiry?.baths && data?.enquiry?.baths?.length > 0">
                                            {{data?.enquiry?.baths && data?.enquiry?.baths?.length > 0 ?
                                            data?.enquiry?.baths :
                                            '--'}}</h6>
                                        <h6 *ngIf="!data?.enquiry?.baths && !data?.enquiry?.baths?.length > 0">--</h6>

                                    </div>
                                </div>
                            </div>
                        </ng-container>
                        <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                            <div class="mt-10 d-flex fw-semi-bold"
                                *ngIf="data?.enquiry?.propertyTypes?.[0]?.displayName == 'Commerical' || data?.enquiry?.propertyTypes?.[0]?.displayName == 'Residential'">
                                <span class="ic-chair ic-light-gray ic-xxs mr-12"></span>
                                <div [ngClass]="data?.enquiry?.furnished ? 'flex-col':'d-flex'">
                                    <span class="text-sm text-dark-gray mr-4">furnish status</span>
                                    <div class="d-flex text-wrap">
                                        <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                            *ngIf="data?.enquiry?.furnished">
                                            {{data?.enquiry?.furnished ? FurnishStatus[data?.enquiry?.furnished] :
                                            '--'}}</h6>
                                        <h6 *ngIf="!data?.enquiry?.furnished">--</h6>

                                    </div>
                                </div>
                            </div>
                            <div class="mt-10 d-flex fw-semi-bold"
                                *ngIf="data?.enquiry?.propertyTypes?.[0]?.displayName == 'Commerical' || data?.enquiry?.propertyTypes?.[0]?.displayName == 'Residential'">
                                <span class="ic-stairs-solid ic-light-gray ic-xxs mr-12"></span>
                                <div
                                    [ngClass]="data?.enquiry?.floors && data?.enquiry?.floors?.length > 0 ? 'flex-col':'d-flex'">
                                    <span class="text-sm text-dark-gray mr-4">preferred floors</span>
                                    <div class="d-flex text-wrap break-all">
                                        <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                            *ngIf="data?.enquiry?.floors && data?.enquiry?.floors?.length > 0">
                                            {{data?.enquiry?.floors && data?.enquiry?.floors?.length > 0 ?
                                            data?.enquiry?.floors : '--'}}</h6>
                                        <h6 *ngIf="!data?.enquiry?.floors && !data?.enquiry?.floors?.length > 0">--</h6>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-10 d-flex fw-semi-bold">
                                <span class="ic-person-percantage ic-light-gray ic-xxs mr-12"></span>
                                <div [ngClass]="data?.enquiry?.offerType ? 'flex-col':'d-flex'">
                                    <span class="text-sm text-dark-gray mr-4">offering type</span>
                                    <div class="d-flex text-wrap">
                                        <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                            *ngIf="data?.enquiry?.offerType">
                                            {{data?.enquiry?.offerType ?
                                            OfferType[data?.enquiry?.offerType] : '--'}} </h6>

                                        <h6 *ngIf="!data?.enquiry?.offerType">--</h6>

                                    </div>
                                </div>
                            </div>
                        </ng-container>
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-circle-user ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.referralName ? 'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">referral name</span>
                                <div class="d-flex text-wrap">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngIf="data?.referralName">
                                        <h5 class="fw-600 break-all">{{data?.referralName ?
                                            data?.referralName:"--" }}</h5>
                                    </h6>
                                    <h6 *ngIf="!data?.referralName">--</h6>

                                </div>
                            </div>
                        </div>
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-phone-ring-solid ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.referralContactNo ? 'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">referral number</span>
                                <div class="d-flex text-wrap">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngIf="data?.referralContactNo">
                                        <h5 class="fw-600 break-all">{{data?.referralContactNo ?
                                            data?.referralContactNo:"--" }}</h5>
                                    </h6>
                                </div>
                                <h6 *ngIf="!data?.referralContactNo">--</h6>

                            </div>
                        </div>
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-envelope-solid ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.referralEmail ? 'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">referral email</span>
                                <div class="d-flex text-wrap">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngIf="data?.referralEmail">
                                        <h5 class="fw-600 break-all">{{data?.referralEmail ?
                                            data?.referralEmail : "--" }}</h5>
                                    </h6>
                                    <h6 *ngIf="!data?.referralEmail">--</h6>

                                </div>
                            </div>
                        </div>

                        <div class="mt-10 align-center fw-semi-bold">
                            <div class="d-flex w-50">
                                <span class="ic-decrease-rupees ic-light-gray ic-xxs mr-8"></span>
                                <div>
                                    <span class="text-sm text-dark-gray mr-4">min. budget</span>
                                    <h6 class="text-truncate-1 break-all">{{ data?.enquiry?.lowerBudget ?
                                        formatBudget(data?.enquiry?.lowerBudget,
                                        data?.enquiry?.currency || defaultCurrency) : '--' }}</h6>
                                </div>
                            </div>
                            <div class="d-flex w-50">
                                <span class="ic-increase-rupees ic-light-gray ic-xxs mr-8"></span>
                                <div>
                                    <span class="text-sm text-dark-gray mr-4">max. budget</span>
                                    <h6 class="text-truncate-1 break-all">{{ data?.enquiry?.upperBudget ?
                                        formatBudget(data?.enquiry?.upperBudget,
                                        data?.enquiry?.currency || defaultCurrency) : '--' }}</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cursor-pointer align-center ml-20 mt-30" (click)="additionalInfo = !additionalInfo">
                        <span class="ic-triangle-down icon ic-coal ic-xxxs mr-12"
                            [ngClass]="{ 'rotate-270' : !additionalInfo}"></span>
                        <h5 class="fw-700 text-black-200">Additional Info</h5>
                    </div>
                    <div class="border-bottom-slate-20 mt-10 ml-40 mr-20"></div>
                    <div class="ml-24 mr-10" *ngIf="additionalInfo">
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-person-suitcase ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.profession ? 'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">profession</span>
                                <div class="d-flex text-wrap">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngIf="data?.profession">
                                        <h5 class="fw-600 break-all">{{data?.profession ?
                                            getProfession(data.profession) :
                                            "--"}}</h5>
                                    </h6>
                                    <h6 *ngIf="!data?.profession">--</h6>
                                </div>
                            </div>
                        </div>
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-person-company ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.companyName
                            ? 'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">company name</span>
                                <div class="d-flex text-wrap">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngIf="data?.companyName">
                                        <h5 class="fw-600 break-all">{{data?.companyName
                                            ?
                                            data?.companyName
                                            :
                                            "--"}}</h5>
                                    </h6>
                                    <h6 *ngIf="!data?.companyName">--</h6>
                                </div>
                            </div>
                        </div>
                        <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                            <div class="mt-10 align-center fw-semi-bold">
                                <span class="ic-flag ic-light-gray ic-xxs mr-12"></span>
                                <span class="text-sm text-dark-gray mr-4 text-nowrap">nationality:</span>
                                <h6 class="text-truncate-1 break-all">{{data?.nationality ? data?.nationality : '--'}}
                                </h6>
                            </div>
                        </ng-container>
                    </div>
                    <div class="cursor-pointer align-center ml-20 mt-30" (click)="others = !others">
                        <span class="ic-triangle-down icon ic-coal ic-xxxs mr-12"
                            [ngClass]="{ 'rotate-270' : !others}"></span>
                        <h5 class="fw-700 text-black-200">others</h5>
                    </div>
                    <div class="border-bottom-slate-20 mt-10 ml-40 mr-20"></div>
                    <div class="ml-24 mr-10" *ngIf="others">
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-serial-number ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.serialNumber ? 'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">serial number</span>
                                <div class="d-flex text-wrap">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngIf="data?.serialNumber">
                                        <h5 class="fw-600 break-all">{{data?.serialNumber ?
                                            data?.serialNumber :
                                            "--"}}</h5>
                                    </h6>
                                    <h6 *ngIf="!data?.serialNumber">--</h6>
                                </div>
                            </div>
                        </div>
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-user-tie ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.sourcingManager ? 'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">sourcing manager</span>
                                <div class="d-flex text-wrap">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngIf="data?.sourcingManager">
                                        <h5 class="fw-600 break-all">{{data?.sourcingManager ?
                                            getName(data.sourcingManager) :
                                            "--"}}</h5>
                                    </h6>
                                    <h6 *ngIf="!data?.sourcingManager">--</h6>
                                </div>
                            </div>
                        </div>
                        <div class="mt-10 d-flex fw-semi-bold">
                            <span class="ic-user-tie ic-light-gray ic-xxs mr-12"></span>
                            <div [ngClass]="data?.closingManager ? 'flex-col':'d-flex'">
                                <span class="text-sm text-dark-gray mr-4">closing manager</span>
                                <div class="d-flex text-wrap">
                                    <h6 class="mt-6 mr-4 px-6 py-2 border bg-light-pearl br-12"
                                        *ngIf="data?.closingManager">
                                        <h5 class="fw-600 break-all">{{data?.closingManager ?
                                            getName(data?.closingManager) :
                                            "--"}}</h5>
                                    </h6>
                                    <h6 *ngIf="!data?.closingManager">--</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="data?.enquiry?.addresses?.length" class="cursor-pointer align-center ml-20 mt-30"
                        (click)="locationInfo = !locationInfo">
                        <span class="ic-triangle-down icon ic-coal ic-xxxs mr-12"
                            [ngClass]="{ 'rotate-270' : !locationInfo}"></span>
                        <h5 class="fw-700 text-black-200">Location Info</h5>
                    </div>
                    <div class="border-bottom-slate-20 mt-10 ml-40 mr-20" *ngIf="data?.enquiry?.addresses?.length">
                    </div>
                    <ng-container *ngFor="let address of data?.enquiry?.addresses">
                        <div class="ml-24 my-10 text-truncate-1 break-all align-center"
                            *ngIf="getLocationDetailsByObj(address) && locationInfo">
                            <span class="dot dot-xxs bg-slate-250 mr-8"> </span>
                            <span> {{getLocationDetailsByObj(address)}}
                            </span>
                        </div>
                    </ng-container>
                </div>
            </div>
            <div class="border br-4 bg-white w-55 ip-w-100 mr-20 ip-mt-10">
                <div class="py-12 px-20 border-bottom">Status</div>
                <div class="scrollbar h-100-176 ip-h-100-390" [ngClass]="{
                            'pe-none grid-blur': (data?.isArchived || data?.isConvertedToLead)
                        }">
                    <div class="m-10 bg-light-pearl br-4 px-10 py-12">
                        <div class="d-flex">
                            <span class="ic-circle-exclamation ic-light-gray ic-xxs mr-4"></span>
                            <span class="text-sm text-dark-gray mr-4">Current Status :</span>
                            <h6 class="fw-semi-bold">{{data?.status?.displayName}}</h6>
                        </div>
                        <div class="d-flex mt-6" *ngIf="data?.scheduleDate">
                            <span class="ic-appointment ic-light-gray ic-xxs mr-4"></span>
                            <span class="text-sm text-dark-gray mr-4">Scheduled date & time :</span>
                            <h6 class="fw-semi-bold">{{getTimeZoneDate(data?.scheduleDate,
                                userBasicDetails?.timeZoneInfo?.baseUTcOffset,'dateWithTime')}}</h6>
                        </div>
                        <div class="d-flex mt-6">
                            <span class="ic-person-secondary ic-light-gray ic-xxs mr-4"></span>
                            <span class="text-sm text-dark-gray mr-4">Last modified by :</span>
                            <h6 class="fw-semi-bold">{{statusModified?.modifiedBy || "--"}}</h6>
                        </div>
                        <div class="d-flex mt-6" *ngIf="statusModified?.modifiedOn">
                            <span class="ic-clock-eight ic-light-gray ic-xxs mr-4"></span>
                            <span class="text-sm text-dark-gray mr-4">Last modified on :</span>
                            <h6 class="fw-semi-bold">{{getTimeZoneDate(statusModified?.modifiedOn,
                                userBasicDetails?.timeZoneInfo?.baseUTcOffset,'dateWithTime')}}
                            </h6>
                        </div>
                    </div>
                    <div class="border-bottom-slate-20 my-10"></div>
                    <div class="text-red ml-20 mb-10" *ngIf="isUnassignedData">Data status can be updated only after
                        they are assigned to someone</div>
                    <form [formGroup]="statusUpdateForm" [ngClass]="{'grid-blur': isUnassignedData}">
                        <div class="px-10">
                            <h5 class="fw-semi-bold text-black-100 mb-10">Update status to</h5>
                            <div class="d-flex flex-wrap">
                                <div *ngFor="let status of statusList" class="oval-radio">
                                    <input type="radio" class="btn-check" name="statusId" [id]="status?.id"
                                        autocomplete="off" formControlName="statusId" [value]="status?.id"
                                        (click)="updateStatus(status);trackingService.trackFeature('Web.Data.Button.UpdateStatusTo.Click')" />
                                    <label class="btn-outline mr-10 mb-10 br-5"
                                        [for]="status?.id">{{status?.displayName}}</label>
                                </div>
                            </div>
                            <form-errors-wrapper [control]="statusUpdateForm.controls['statusId']"
                                label="{{'GLOBAL.status' | translate}}"></form-errors-wrapper>
                            <div *ngIf="selectedStatusList?.length">
                                <div *ngFor="let fields of selectedStatusList">

                                    <div *ngIf="fields?.field?.name === 'ScheduledDate'" class="d-flex ph-flex-col"
                                        [ngClass]="{'mt-20': !statusUpdateForm?.valid && !statusUpdateForm?.controls?.['statusId']?.value, 'blinking pe-none': isStatusListLoading}">
                                        <div class="w-50 ph-w-100 ph-mr-0 mr-20">
                                            <div [ngClass]="{'field-label-req' : (fields?.isRequired || fields?.validators[0]==='required')}"
                                                class="field-label"> Schedule date</div>
                                            <div class="position-relative">
                                                <form-errors-wrapper
                                                    [control]="statusUpdateForm.controls['scheduleDate']"
                                                    label="Schedule Date">
                                                    <input type="text" readonly [min]="minDate"
                                                        class="px-16 py-8 fw-600 text-sm br-4 mt-4 outline-0"
                                                        [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                                                        placeholder="DD/MM/YYYY" formControlName="scheduleDate" />
                                                    <owl-date-time [pickerType]="'calendar'" #dt1
                                                        [startAt]="statusUpdateForm.controls?.scheduleDate?.value ? null : currentDate"
                                                        (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                                                </form-errors-wrapper>
                                                <div *ngIf="statusUpdateForm.controls['scheduleDate'].value"
                                                    class="position-absolute right-5 top-18 cursor-pointer">
                                                    <span (click)="removeScheduler('date')"
                                                        title="remove schedule date and time"
                                                        class="icon ic-dark ic-refresh ic-xxxs">
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="w-50 ph-w-100 ph-mr-0 mr-20 ph-mt-10"
                                            [ngClass]="{'pe-none': !statusUpdateForm.controls?.scheduleDate?.value}">
                                            <div class="field-label"
                                                [ngClass]="{'text-dark-gray': !statusUpdateForm.controls?.scheduleDate?.value,'field-label-req':fields?.isRequired}">
                                                Schedule time</div>
                                            <div class="position-relative">
                                                <form-errors-wrapper
                                                    [control]="statusUpdateForm.controls['scheduleTime']"
                                                    label="Schedule Time">
                                                    <input [disabled]="!statusUpdateForm.controls?.scheduleDate?.value"
                                                        type="text" readonly
                                                        class="px-16 py-8 fw-600 text-sm br-4 mt-4 outline-0"
                                                        [owlDateTimeTrigger]="dt2" [owlDateTime]="dt2"
                                                        placeholder="00:00 AM" formControlName="scheduleTime" />
                                                    <owl-date-time [pickerType]="'timer'" [hour12Timer]="true"
                                                        [startAt]="statusUpdateForm.controls?.scheduleTime?.value ? null:  currentDate"
                                                        (afterPickerOpen)="onPickerOpened(currentDate)"
                                                        #dt2></owl-date-time>
                                                </form-errors-wrapper>
                                                <div *ngIf="statusUpdateForm.controls['scheduleTime'].value"
                                                    class="position-absolute right-5 top-18 cursor-pointer">
                                                    <span (click)="removeScheduler('time')" title="remove schedule time"
                                                        class="icon ic-dark ic-refresh ic-xxxs">
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <span *ngIf="statusUpdateForm.controls['statusId'].value">
                                <div class="field-label">Notes</div>
                                <div class="form-group mb-10">
                                    <textarea formControlName="notes" class="non-resizable scrollbar" rows="4"
                                        placeholder="Add a note .... "></textarea>
                                </div>
                            </span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="w-40pr tb-w-100 tb-mt-10" [ngClass]="{'blinking pe-none': isHistoryLoading}">
            <div class="border br-20 bg-white align-center user w-max-content">
                <div class="activation" [ngClass]="{'active' : selectedSection == 'History'}"
                    (click)="selectedSection = 'History'; notesDirty = false">
                    <div class="align-center ip-d-flex">
                        <span class="icon ic-notes-secondary ic-sm mr-8 ip-mr-4"
                            [ngClass]="{'active' : selectedSection !== 'History'}"></span>
                        <span>History</span>
                    </div>
                </div>
                <div class="activation" [ngClass]="{'active' : selectedSection == 'Notes'}"
                    (click)="selectedSection = 'Notes'" *ngIf="(!data?.isArchived && !data?.isConvertedToLead)">
                    <div class="align-center ip-d-flex">
                        <span class="icon ic-notes-pen ic-sm mr-8 ip-mr-4"
                            [ngClass]="{'active' : selectedSection !== 'Notes'}"></span>
                        <span>Notes</span>
                    </div>
                </div>
            </div>
            <div class="mt-30" *ngIf="selectedSection == 'History'">
                <data-history [history]="history" [formDirty]="statusUpdateForm?.dirty || notesDirty"></data-history>
            </div>
            <div class="mt-30" *ngIf="selectedSection == 'Notes'">
                <data-notes (updateFormDirty)="handleUpdateFormDirty($event)" [history]="history"
                    [formDirty]="statusUpdateForm?.dirty" [dataId]="data?.id"></data-notes>
            </div>
        </div>
    </div>
    <div *ngIf="isDataLoading" class="flex-center w-100 h-100-250 min-h-250">
        <div class="flex-center h-100">
            <application-loader></application-loader>
        </div>
    </div>
</div>
<div [ngClass]="statusUpdateForm?.dirty || notesDirty ? 'ph-h-75px' : ''"
    class="flex-between h-55px ph-flex-col ph-flex-between-unset px-20 py-10 bg-white box-shadow-20 position-absolute bottom-0 w-100-140 tb-w-100 fw-semi-bold text-large">
    <div class="flex-between">
        <div class="align-center">
            <!-- Do not remove this code -->
            <!-- <div class="fw-600 text-black-200 text-decoration-underline mr-16 cursor-pointer">Close
            </div>
            <div class="br-4 p-10 border mr-10 cursor-pointer text-dark-gray">Cancel
            </div> -->
        </div>
    </div>
    <div class="flex-end ph-flex-col">
        <div class="flex-between" *ngIf="statusUpdateForm?.dirty || notesDirty">
            <!-- Do not remove this code -->
            <div class="fw-600 text-black-200 text-decoration-underline cursor-pointer"
                (click)="close();trackingService.trackFeature('Web.Data.Button.DataPreviewClose.Click')">
                Close</div>
            <div class="border-right mx-12 h-16"></div>
            <div class="fw-600 text-black-200 text-decoration-underline cursor-pointer"
                (click)="save(true,false);trackingService.trackFeature('Web.Data.Button.DataPreviewSaveAndClose.Click')">
                Save & Close</div>
            <div class="border-right mx-12 h-16"></div>
            <div class="fw-600 text-black-200 text-decoration-underline cursor-pointer"
                (click)="save(false,true);trackingService.trackFeature('Web.Data.Button.DataPreviewSaveAndGoToNext.Click')">
                Save & Go To Next</div>
        </div>
        <div *ngIf="statusUpdateForm?.dirty || notesDirty" class="border-right mx-12 h-16 ph-d-none"></div>
        <div class="ph-mt-10" [ngClass]="statusUpdateForm?.dirty || notesDirty ? 'align-center' : 'flex-between'">
            <ng-container *ngIf="statusUpdateForm?.dirty || notesDirty">
                <div class="btn-coal min-w-60" (click)="save(false,false)">Save</div>
                <div class="border-right mx-12 h-16"></div>
            </ng-container>
            <div class="bg-coal icon-badge"
                [ngClass]="{'bg-black-200 pe-none' : validateButton('prev'), 'blinking': (isDataLoading || isHistoryLoading || allDataLoading)}"
                (click)="prevData();trackingService.trackFeature('Web.Data.Button.PrevData.Click')">
                <span class="icon ic-chevron-left m-auto ic-xs"></span>
            </div>
            <div class="border-right mx-12 h-16"></div>
            <div class="bg-coal icon-badge"
                [ngClass]="{'bg-black-200 pe-none' : validateButton('next'), 'blinking': (isDataLoading || isHistoryLoading || allDataLoading)}"
                (click)="nextData();trackingService.trackFeature('Web.Data.Button.NextData.Click')">
                <span class="icon ic-chevron-right m-auto ic-xs"></span>
            </div>
        </div>
    </div>
</div>

<ng-template #confirmationPopup>
    <div class="br-4 pb-20">
        <h2 class="waring-bg-pattern h-100px align-center text-white fw-800 text-xl p-20 brtr-4 brtl-4">Already a
            lead has been created with this contact number.
            Do you want to proceed?</h2>
        <div class="text-black-200 m-10 p-10 text-large br-4">This will create duplicate lead.
        </div>
        <div class="flex-end mt-30 pr-10">
            <div class="btn-gray mr-20" (click)="modalRef.hide()">
                Cancel</div>
            <div class="btn btn-sm text-mud w-90 br-5 fw-600 flex-center text-large bg-black text-white"
                data-automate-id="deleteYes" (click)="openConvertLeadModal();modalRef.hide()">
                Proceed</div>
        </div>
    </div>
</ng-template>

<ng-template #virtualNo>
    <a class="ic-close-secondary ic-close-modal ip-ic-close-modal" (click)="closeModal()"></a>
    <div class="p-16 fw-600 flex-center-col mt-20">
        <div>Select virtual Number</div>
        <div class="w-100 mt-8">
            <ng-select [virtualScroll]="true" placeholder="Select virtual Number" name="virtualNo"
                [items]="virtualNosList" ResizableDropdown [(ngModel)]="selectedVirtualNo">
            </ng-select>
        </div>
        <div class="flex-center mt-20">
            <button class="btn-gray" id="btnVirtualCancel" data-automate-id="btnVirtualCancel" (click)="closeModal()">
                {{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal ml-8" (click)="ivrClickToCheck(selectedVirtualNo, chooseToCallType)">
                {{ 'BUTTONS.connect' | translate }}</button>
        </div>
    </div>
</ng-template>

<ng-template #askBefore>
    <div class="p-20 flex-center-col">
        <h3 class="text-black-100 fw-semi-bold mb-10">choose which type you want to call?</h3>
        <div class="flex-center fw-600 py-20">
            <div class="flex-center-col">
                <img alt="ivr" src="../../../../assets/images/integration/ivr-logo.svg" class="mb-8 cursor-pointer"
                    width="40" height="40" (click)="openIVROnly(virtualNo, contactAdmin, chooseToCallType)">
                <div>IVR</div>
            </div>
            <div class="flex-center-col ml-20">
                <div (click)="initCall($event)" class="br-6 mb-8 bg-accent-blue cursor-pointer">
                    <span class="icon ic-Call ic-large m-10"></span>
                </div>
                <div>Dialer Pad</div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #contactAdmin>
    <div class="p-16 flex-center-col">
        <h4 class="text-gray fw-600 mt-20 mb-30 text-center">{{ contactAdminType }}</h4>
        <button class="btn-coal" (click)="modalRef.hide()">Ok, Got it</button>
    </div>
</ng-template>

<ng-template #chooseToCallType>
    <div class="p-20">
        <h3 class="text-black-100 fw-semi-bold mb-10">choose which number you want to call?</h3>
        <div class="align-center">
            <div class="form-check form-check-inline" (click)="selectedCallType = params.data?.contactNo">
                <input type="radio" id="inpPhoneNumber" data-automate-id="inpPhoneNumber" class="radio-check-input"
                    name="callType" checked>
                <div class="fw-600 text-secondary cursor-pointer text-large" for="inpPhoneNumber">
                    Primary Number
                </div>
            </div>
        </div>
        <div class="align-center">
            <div class="form-check form-check-inline" (click)="selectedCallType = params.data?.alternateContactNo">
                <input type="radio" id="inpAlternateNumber" data-automate-id="inpAlternateNumber"
                    class="radio-check-input" name="callType">
                <div class="fw-600 text-secondary cursor-pointer text-large" for="inpAlternateNumber">
                    {{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' | translate}}
                </div>
            </div>
        </div>
        <div class="flex-end mt-30">
            <button class="btn-gray" id="clkCallNumberCancel" data-automate-id="clkCallNumberCancel"
                (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-green ml-20" id="clkCallNumberConfirm" data-automate-id="clkCallNumberConfirm"
                (click)="ivrClickToCall(VNAssignment?.virtualNumber || selectedVirtualNo, true)">
                {{ 'DASHBOARD.call' | translate }}</button>
        </div>
    </div>
</ng-template>

<ng-template #mobileNotification>
    <div class="p-20">
        <h3 class="text-black-100 fw-semi-bold mb-10">we have sent call notification to mobile
        </h3>
        <div class="flex-end mt-30">
            <button class="btn-gray" id="clkCallNumberCancel" data-automate-id="clkCallNumberCancel"
                (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-green w-150 ml-20" id="clkCallNumberConfirm" data-automate-id="clkCallNumberConfirm"
                (click)="openDialerPad()">
                Choose Another App</button>
        </div>
    </div>
</ng-template>