<div class="bg-white h-100vh tb-w-100-40">
  <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
    <h3 class="fw-semi-bold">{{ isMeeting ? 'Meetings' : globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referrals' : 'Site Visits' }} {{
      'LOCATION.location' | translate}}</h3>
    <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="closeModal()"></div>
  </div>
  <div class="d-flex">
    <div class="flex-column">
      <div class="field-label clear-margin fw-semi-bold text-mud p-12 ph-d-none">{{ 'LEADS.location-punch-in' |
        translate }}</div>
      <div
        class="scrollbar h-100-80 scroll-hide ph-position-absolute ph-bottom-0 z-index-2 ph-d-flex ph-h-unset ph-w-100-40 bg-white">
        <ng-container *ngFor="let entry of data; index as i">
          <div *ngIf="i != 0" class="border-bottom ml-12 mr-12 bg-light-pearl ph-d-none"></div>
          <div [title]="(entry?.location?.subLocality && entry?.location?.city) ? (entry?.location?.subLocality + ', ' + entry?.location?.city)
          : (entry?.location?.city || entry?.location?.subLocality || 'Location not available')"
            class="m-12 p-8 cursor-pointer w-180 ph-min-w-200 bg-light-pearl border-right border-0 br-6 position-relative"
            (click)="showLocation(entry, i+1)">
            <div class="flex-between">
              <div class="icon ic-location-solid ic-accent-green ic-sm mr-4 position-absolute top-4 right-0"></div>
              <div class="flex-column text-sm text-mud">
                <h5 class="fw-600 mb-6 text-decoration-underline">{{ isMeeting ? 'Meeting' :
                  globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral' : 'Site Visit' }} {{ isMeeting ? 'Done' : globalSettingsData?.shouldRenameSiteVisitColumn ? 'Taken' : 'Done' }}</h5>
                <div *ngIf="entry?.projectName"><span class="fw-700">Project Name:</span> {{entry.projectName}}</div>
                <div *ngIf="entry?.executiveName"><span class="fw-700">Sales Executive Name:</span>
                  {{entry.executiveName}}</div>
                <div *ngIf="entry?.executiveContactNo"><span class="fw-700">Sales Executive Number:</span>
                  {{entry.executiveContactNo}}</div>
                <div *ngIf="entry?.notes"><span class="fw-700">Notes:</span> {{entry.notes}}</div>
                <div *ngIf="entry?.imagesWithName?.length" class="align-center flex-wrap">
                  <span class="fw-700">Uploaded Documents:</span>
                  <ng-container *ngFor="let doc of entry?.imagesWithName; let last = last">
                    <a [href]="s3BucketPath+doc?.filePath" target="_blank"
                      [ngClass]="doc?.filePath ? 'cursor-pointer text-dark-hover' : 'pe-none'">{{doc?.documentName
                      ? doc?.documentName : 'Document'}}{{!last ? ', ': ' '}}</a>
                  </ng-container>
                </div>
                <div class="text-dark-gray text-xxs mt-4"><span class="mr-4">{{'GLOBAL.by' | translate}}</span>
                  <span class="fw-700">{{getAssignedToDetails(entry?.lastModifiedBy, userList, true) || ''}}</span>
                </div>
                <div class="text-dark-gray text-xxs"><span class="fw-700">{{ entry?.lastModifiedOn ?
                    getTimeZoneDate(entry?.lastModifiedOn, userData?.timeZoneInfo?.baseUTcOffset, 'fullDateTime'):
                    ''}}</span>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
    <div class="w-80pr ph-w-100 responsive-map">
      <google-map [center]="{lat: center?.lat, lng: center?.lng}">
        <map-marker #mapMarker="mapMarker" (mapClick)="openInfoWindow(mapMarker)" *ngFor="let marker of markers"
          [position]="marker?.latitude ? {lat:marker?.latitude, lng:marker?.longitude} : ''" [label]="marker?.label">
        </map-marker>
        <map-info-window>{{selectedAddress}}</map-info-window>
      </google-map>
    </div>
  </div>
</div>