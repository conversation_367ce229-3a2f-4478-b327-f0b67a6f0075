import { EventEmitter, Injectable, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { catchError, filter, firstValueFrom, map, of, skipWhile, switchMap, take, takeUntil } from 'rxjs';

import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { getTenantName, isEmptyObject } from 'src/app/core/utils/common.util';
import {
  FetchGlobalSettingsAnonymous,
  FetchOTPGlobalSettings,
} from 'src/app/reducers/global-settings/global-settings.actions';
import {
  getGlobalSettingsAnonymous,
  getOTPGlobalSettings,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  Login,
  LoginActions,
  LoginSuccess,
  StoreUsername,
  TryAnotherWay,
  TryAnotherWaySuccess,
  TwoFactorOtpGeneration,
  TwoFactorOtpGenerationSuccess,
  TwoFactorOtpVerify,
  clearOtpDetails
} from 'src/app/reducers/login/login.actions';
import {
  FetchLeadStatusList,
  FetchProjectTypes,
  FetchPropertyTypesList,
} from 'src/app/reducers/master-data/master-data.actions';
import {
  getProjectTypes,
  getPropertyTypes,
  getStatusMasterData,
} from 'src/app/reducers/master-data/master-data.reducer';
import { UpdatePermissions } from 'src/app/reducers/permissions/permissions.actions';
import {
  getPermissions,
  getPermissionsIsLoading,
} from 'src/app/reducers/permissions/permissions.reducers';
import { FetchUserById } from 'src/app/reducers/teams/teams.actions';
import { getSelectedUser } from 'src/app/reducers/teams/teams.reducer';
import { LoginService } from 'src/app/services/controllers/login.service';
import { PermissionsService } from 'src/app/services/shared/permissions.service';

@Injectable()
export class LoginEffects implements OnDestroy {
  login$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(LoginActions.LOGIN),
        map((action: Login) => action.resource),
        switchMap((data: any) => {
          return this.loginService.login(data).pipe(
            map((resp: any) => {
              let userDetails = this.parseJwt(resp?.data?.idToken);
              if (userDetails['custom:mfa_enabled'] === 'True') {
                this.store.dispatch(new FetchOTPGlobalSettings());
                this.store
                  .select(getOTPGlobalSettings)
                  .subscribe((data: any) => {
                    if (data?.succeeded && data?.data?.isEnabled) {
                      let username = userDetails['preferred_username'];
                      this.store.dispatch(new TwoFactorOtpGeneration(username));
                      this.store.dispatch(new StoreUsername(username));
                    } else if (data?.succeeded && !data?.data?.isEnabled) {
                      return this.loginEffectLogic(resp);
                    }
                  });
              } else if (
                (userDetails['custom:mfa_enabled'] === 'False' &&
                  resp?.data?.idToken) ||
                resp?.data?.refreshToken
              ) {
                return this.loginEffectLogic(resp);
              }
            }),
            catchError((err: any) => {
              this._notificationService.error(
                err?.error?.messages?.[0]
                  ? err?.error?.messages?.[0]
                  : 'Sorry, please try again later.'
              );
              this.store.dispatch(new LoginSuccess(err))
              return of(new OnError(err));
            })
          );
        }),
      ),
    { dispatch: false }
  );

  verifyOTP$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(LoginActions.OTP_VERIFY),
        switchMap((action: TwoFactorOtpVerify) =>
          this.loginService
            .verifyOTP(action.username, action.sessionId, action.otp)
            .pipe(
              map((resp: any) => {
                if (
                  resp?.data?.idToken ||
                  (resp?.data?.refreshToken && resp.succeeded)
                ) {
                  return this.loginEffectLogic(resp);
                } else {
                  this._notificationService.error(resp.message);
                }
              }),
              catchError((error) => {
                this._notificationService.error(
                  error.error.messages?.[0]
                    ? error.error.messages?.[0]
                    : 'Sorry, please try again later.'
                );
                return of(new OnError(error));
              })
            )
        )
      ),
    { dispatch: false }
  );

  generateOTP$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LoginActions.OTP_GENERATION),
      switchMap((action: TwoFactorOtpGeneration) =>
        this.loginService.generateOTP(action.username).pipe(
          map((resp: any) => {
            if (resp['SessionId']) {
              if (this.router.url === 'login/two-factor-authentication') {
                this._notificationService.success(
                  'Resend OTP Generated Successfully'
                );
              } else {
                this.router.navigate(['login', 'two-factor-authentication']);
                this._notificationService.success('OTP Generated Successfully');
              }
              return new TwoFactorOtpGenerationSuccess(resp);
            } else {
              this._notificationService.error('Error in Generating OTP');
              return null;
            }
          }),
          catchError((error) => {
            return of(new OnError(error));
          })
        )
      )
    )
  );

  tryAnotherWay$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LoginActions.TRY_ANOTHER_WAY),
      switchMap((action: TryAnotherWay) =>
        this.loginService.tryAnotherWay(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('OTP Generated Successfully');
              return new TryAnotherWaySuccess(resp);
            } else {
              return null;
            }
          }),
          catchError((error) => {
            return of(new OnError(error));
          })
        )
      )
    )
  );

  userDetails: string;
  userId: string;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  allPermissions: Array<any>;

  constructor(
    private actions$: Actions,
    private _notificationService: NotificationsService,
    private router: Router,
    private store: Store<AppState>,
    private loginService: LoginService,
    private permissionsService: PermissionsService
  ) { }

  parseJwt(tokenM: any) {
    var base64Url = tokenM.split('.')[1];
    var base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    var jsonPayload = decodeURIComponent(
      window
        .atob(base64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join('')
    );
    return JSON.parse(jsonPayload);
  }

  loginEffectLogic(resp: any) {
    localStorage.setItem('idToken', resp.data.idToken);
    localStorage.setItem('refreshToken', resp.data.refreshToken);
    localStorage.setItem(
      'userDetails',
      JSON.stringify(this.parseJwt(resp.data.idToken))
    );
    this.userDetails = localStorage.getItem('userDetails');
    this.userId = JSON.parse(this.userDetails).sub;
    if (this.userId) {
      this.store.dispatch(new FetchUserById(this.userId));
      this.store.dispatch(new FetchLeadStatusList());
      this.store.dispatch(new FetchProjectTypes());
      this.store.dispatch(new FetchGlobalSettingsAnonymous());
      this.store
        .select(getGlobalSettingsAnonymous)
        .pipe(
          filter((data: any) => data && !!Object.keys(data).length),
          take(1)
        )
        .subscribe((data: any) => {
          if (data?.shouldEnablePropertyListing) {
            this.store.dispatch(new FetchPropertyTypesList('listing'));
          } else {
            this.store.dispatch(new FetchPropertyTypesList());
          }
        });

      this.store
        .select(getSelectedUser)
        .pipe(takeUntil(this.stopper))
        .subscribe((item: any) => {
          if (
            item &&
            this.userId &&
            !isEmptyObject(item) &&
            item?.userId === this.userId
          ) {
            // Check if user has Admin role
            const isAdmin = item.rolePermission.some(
              (permissions: Record<string, any>) =>
                permissions.name.toLowerCase() === 'admin'
            );
            localStorage.setItem('isAdmin', isAdmin.toString());

            this.allPermissions = Object.values(
              item.rolePermission.filter(
                (permissions: Record<string, any>) =>
                  permissions.name.toLowerCase() != 'default'
              )
            ).map((data: any) => {
              return data.permissions;
            });
            const flatArray = this.allPermissions.flat();
            const distinctPermissions: any = [...new Set(flatArray)];
            this.store.dispatch(new UpdatePermissions(distinctPermissions));
            localStorage.setItem('userPermissions', distinctPermissions);
            this.permissionsService.routeToUrl(
              this.router.url,
              distinctPermissions
            );
          }
        });

      this.store
        .select(getStatusMasterData)
        .pipe(takeUntil(this.stopper))
        .subscribe(async (data: any) => {
          if (data?.length) {
            const globalSettingsData:any = await firstValueFrom(
              this.store.select(getGlobalSettingsAnonymous).pipe(skipWhile((data) => !Object.keys(data).length))
            );
            let status:any = [...data];
            if(globalSettingsData?.shouldRenameSiteVisitColumn) {
              status = status?.map((item: any) => {
                if(item.displayName === 'Site Visit Scheduled') {
                  return {...item, displayName: 'Referral Scheduled',actionName: 'Schedule Referral'}
                }
                return {...item}
              })
            }
            const allLeadStatus: any = JSON.stringify(status);
            localStorage.setItem('masterleadstatus', allLeadStatus);
          }
        });

      this.store
        .select(getProjectTypes)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          if (data?.items?.length) {
            let projectTypeList = JSON.stringify(data?.items);
            localStorage.setItem('projectType', projectTypeList);
          }
        });

      this.store
        .select(getPropertyTypes)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          if (data?.length) {
            let propertyTypeList = JSON.stringify(data);
            localStorage.setItem('propertyType', propertyTypeList);
          }
        });

      let subDomain = getTenantName();
      localStorage.setItem('subDomain', subDomain);

      let permissionsSet: any;
      this.store
        .select(getPermissions)
        .pipe(takeUntil(this.stopper))
        .subscribe((permissions: any) => {
          if (!permissions?.length) return;
          permissionsSet = new Set(permissions);
        });

      this.store
        .select(getPermissionsIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: boolean) => {
          if (!isLoading) {
            if (permissionsSet?.has('Permissions.Leads.View')) {
              this.router.navigate(['/leads']);
            } else {
              this.router.navigate(['teams/user-details', this.userId]);
            }
          }
        });
    }
    this.store.dispatch(new clearOtpDetails());
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
