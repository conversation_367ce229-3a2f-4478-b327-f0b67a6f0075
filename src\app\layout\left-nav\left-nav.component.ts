import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  EventEmitter,
  Host<PERSON><PERSON><PERSON>,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { ActivatedRoute, Router } from '@angular/router';
import { SwUpdate } from '@angular/service-worker';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';

import { APP_VERSION, HEADER_LIST } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getAppImages, getTenantName } from 'src/app/core/utils/common.util';
import { SupportComponent } from 'src/app/features/login/support/support.component';
import { AddTaskComponent } from 'src/app/features/task/add-task/add-task.component';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  getPermissions,
  getPermissionsIsLoading,
  getViewPermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
import { getProducts } from 'src/app/reducers/this/products/products.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'left-nav',
  templateUrl: './left-nav.component.html',
})
export class LeftNavComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  versionNo: string = APP_VERSION;
  showLeftNav: boolean = true;
  showLeftNavPopup: boolean = false;
  header: any = HEADER_LIST;
  titles: string = '';
  canView: string[] = [];
  permissionsIsLoading: boolean = true;
  permissionsSet: Set<unknown>;
  heightFromTop: number;
  receivedCurrentPath: string;
  isSubNavbarVisible: boolean = true;
  globalSettingsDetails: any;
  online: AnimationOptions = {
    path: 'assets/animations/circle-green.json',
  };
  newUpdateAvailable: boolean;
  isViewlisting: any;
  isProdEnv: boolean = env.production;
  appImages: { appLogo: string; appText: string } = getAppImages();
  subDomain = getTenantName();
  productsList: any;
  selectedAppId: string = '';

  constructor(
    private modalService: BsModalService,
    public router: Router,
    public activatedRoute: ActivatedRoute,
    private headerTitle: HeaderTitleService,
    private store: Store<AppState>,
    public shareDataService: ShareDataService,
    private cdr: ChangeDetectorRef,
    private swUpdate: SwUpdate,
    private afs: AngularFirestore
  ) { }

  ngOnInit(): void {
    this.onResize();
    this.store
      .select(getProducts)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        // Filter to only show active and non-deleted applications
        this.productsList =
          data?.filter((app: any) => app.isActive === true) || [];

        const savedAppId = localStorage.getItem('selectedAppId');
        if (savedAppId && this.productsList?.length) {
          const savedApp = this.productsList.find(
            (app: any) => app.id === savedAppId
          );
          if (savedApp) {
            this.selectedAppId = savedApp.id;
            this.shareDataService.setSelectedApp(savedApp);
          } else {
            // If saved app is no longer active/available, clear the selection
            localStorage.removeItem('selectedAppId');
            this.selectedAppId = '';
          }
        }
      });

    this.shareDataService.URL$.subscribe((data) => {
      this.receivedCurrentPath = data;
    });

    this.swUpdate.available.subscribe((event) => {
      this.swUpdate.activateUpdate().then(() => {
        this.checkForUpdate();
      });
    });

    this.store
      .select(getViewPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canView: any) => {
        this.canView = canView;
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.permissionsSet = new Set(permissions);
      });

    this.store
      .select(getPermissionsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.permissionsIsLoading = isLoading;
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
        this.isViewlisting = data?.shouldEnablePropertyListing;
      });

    this.headerTitle.pageTitle.subscribe((newTitle) => {
      this.titles = newTitle;
    });
  }

  navVerticalScroll() {
    this.isSubNavbarVisible = false;
    this.cdr.detectChanges();
  }

  onMouseEnter(event: MouseEvent) {
    this.isSubNavbarVisible = true;
    const targetElement = event.target as HTMLElement;
    this.heightFromTop =
      targetElement.getBoundingClientRect().top + window.scrollY + 8;
    this.cdr.detectChanges();
  }

  toggleLeftNav(): void {
    this.showLeftNav = !this.showLeftNav;
    this.shareDataService.setShowLeftNav(this.showLeftNav);
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    if (window.innerWidth <= 1279) {
      this.shareDataService.setShowLeftNav(false);
    } else {
      this.shareDataService.setShowLeftNav(true);
    }
  }

  navigateToAddLead() {
    this.router.navigate(['/leads/add-lead']);
  }

  navigateToAddProperty() {
    this.router.navigate(['properties/add-property']);
  }

  stop(event: Event) {
    event.stopPropagation();
  }

  navigateToAddTask() {
    this.modalService.show(AddTaskComponent, {
      class: 'right-modal modal-350',
    });
  }

  openSupport() {
    this.modalService.show(SupportComponent, {
      class: 'modal-300 modal-dialog-centered',
    });
  }

  checkForUpdate(): void {
    this.afs
      .collection('versioning')
      .doc('versioning')
      .valueChanges()
      .subscribe((data: any) => {
        if (data?.version?.currentVersion > APP_VERSION) {
          this.newUpdateAvailable = true;
          return;
        }
      });
  }

  updateVersion() {
    document.location.reload();
  }

  openLinkInNewTab(event: MouseEvent, customRoute?: string): void {
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault();
      const targetRoute =
        customRoute ||
        (event.target as HTMLAnchorElement).getAttribute('routerLink') ||
        'leads';
      window.open(`/${targetRoute}`, '_blank');
    }
  }

  onClick(event: MouseEvent) {
    const targetElement = event.currentTarget as HTMLElement;
    const pointerElement = targetElement.querySelector(
      '.pointer'
    ) as HTMLElement;
    this.heightFromTop =
      pointerElement?.getBoundingClientRect().top + window.scrollY;
    this.isSubNavbarVisible = true;
    this.cdr.detectChanges();
    // this.openFBAlert();
  }

  openFBAlert() {
    localStorage.setItem('closeAlertBox', JSON.stringify(false));
  }

  onAppsClick(event: Event, app: any) {
    event.preventDefault();
    event.stopPropagation();

    if (app?.id && app?.applicationUrl) {
      if (this.selectedAppId !== app.id) {
        this.selectedAppId = app.id;
        localStorage.setItem('selectedAppId', app.id);
        this.shareDataService.setSelectedApp(app);
      }
      if (!this.router.url.includes('/this-applications')) {
        this.router.navigate(['/this-applications']);
      }
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
