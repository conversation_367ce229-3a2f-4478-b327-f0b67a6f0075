import {
  Component,
  EventEmitter,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, firstValueFrom, skipWhile, takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { MeetingSiteVisitDoneComponent } from 'src/app/features/leads/meeting-site-visit-done/meeting-site-visit-done.component';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { addDuplicate } from 'src/app/reducers/lead/lead.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';

@Component({
  selector: 'lead-name-section',
  templateUrl: './lead-name-section.component.html',
})
export class LeadNameSectionComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('allTagsModal') allTagsModal: TemplateRef<any>;
  @ViewChild('confirmModal') confirmModal: TemplateRef<any>;
  params: any;
  canViewDuplicateTag: boolean = false;
  labelItems: any;
  cardData: any;
  leadTags: any = [];
  remainingTags: any;
  allLeadTags: any;
  currentPath: string;
  isClickTd: string;
  showRemainingTags: boolean;
  globalSettingsData: any;

  constructor(
    private _store: Store<AppState>,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    private router: Router,
    public modalRef: BsModalRef,
    private trackingService: TrackingService

  ) { }
 async ngOnInit() {
    this.globalSettingsData = await firstValueFrom(
      this._store
        .select(getGlobalSettingsAnonymous)
        .pipe(skipWhile((data) => !data))
    );
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Leads.ViewDuplicateTag')) {
          this.canViewDuplicateTag = true;
        }
      });

    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;
  }

  agInit(params: any): void {
    this.params = params;
    this.allLeadTags = params?.data?.customFlags?.filter((flag: any) => flag?.flag?.isActive);
    if (this.allLeadTags?.length <= 6) {
      this.leadTags = this.allLeadTags;
      this.remainingTags = [];
      this.showRemainingTags = false;
    } else {
      this.leadTags = this.allLeadTags.slice(0, 5);
      this.remainingTags = this.allLeadTags.slice(5);
      this.showRemainingTags = true;
    }
  }

  openLeadPreviewModal(event: Event) {
    event.stopPropagation();
    let initialState: any = {
      data: this.params.data,
      cardData: this.cardData,
    };
    this.modalService.show(
      LeadPreviewComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-550 ip-modal-unset', initialState }
      )
    );
  }

  openLocationModal(event: any, isMeetingDone: boolean = false) {
    event.stopPropagation();
    let initialState: any = {
      data: isMeetingDone
        ? this.params.data?.meetingsDone
        : this.params.data?.siteVisitsDone,
      isMeeting: isMeetingDone,
    };
    this.modalService.show(MeetingSiteVisitDoneComponent, {
      class: 'right-modal modal-650 ip-modal-unset',
      initialState,
    });
  }

  duplicateHandler(event$: any, isClickTd: string) {
    this.isClickTd = isClickTd;
    if (this.currentPath === '/invoice') {
      this.modalRef = this.modalService.show(this.confirmModal, {
        class: 'modal-500 top-modal ip-modal-unset',
        ignoreBackdropClick: true,
      });
      return;
    }
    let lead = {
      selectedLeadId:
        isClickTd === 'TD' ? this.params.data?.id : this.params.data?.rootId,
    };
    // this._store.dispatch(
    //   new addDuplicate(
    //     true,
    //     isClickTd === 'TD' ? this.params.data?.id : this.params.data?.rootId
    //   )
    // );
    this.shareDataService.triggerDuplicateMethod(lead);
    this.trackingService.trackFeature('Web.Leads.Options.DuplicateLead.Click');
  }

  isClickYesOrNo(isClickYes: boolean) {
    if (isClickYes) {
      let lead = {
        selectedLeadId:
          this.isClickTd === 'TD'
            ? this.params.data?.id
            : this.params.data?.rootId,
      };
      this._store.dispatch(
        new addDuplicate(
          true,
          this.isClickTd === 'TD'
            ? this.params.data?.id
            : this.params.data?.rootId
        )
      );
      this.modalRef.hide();
      this.router.navigate(['leads/manage-leads']);
    } else {
      this.modalRef.hide();
    }
  }

  showAllTags($event: any) {
    $event.stopPropagation();
    this.modalService.show(this.allTagsModal, {
      class: 'modal-550 modal-dialog-centered ip-modal-unset',
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
