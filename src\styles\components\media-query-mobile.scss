@media screen and (max-width: 480px) {
    .ph-w-15 {
        width: 15px !important;
    }

    .ph-w-30 {
        width: 30% !important;
    }

    .ph-w-33 {
        width: 33% !important;
    }

    .display-web {
        display: none !important;
    }

    .display-mble {
        display: contents !important;
    }

    .ph-w-40 {
        width: 40% !important;
    }

    .ph-w-50 {
        width: 50% !important;
    }

    .ph-w-60 {
        width: 60% !important;
    }

    .ph-w-70pr {
        width: 70% !important;
    }

    .ph-w-100 {
        width: 100% !important;
    }

    .ph-w-30px {
        width: 30px !important;
    }

    .ph-w-40px {
        width: 40px !important;
    }

    .ph-w-50px {
        width: 50px !important;
    }

    .ph-w-15 {
        width: 15px !important;
    }

    .ph-w-70 {
        width: 70px !important;
    }

    .ph-w-80px {
        width: 80px !important;
    }

    .ph-w-100px {
        width: 100px !important;
    }

    .ph-w-110px {
        width: 110px !important;
    }

    .ph-w-130px {
        width: 130px !important;
    }

    .ph-w-150px {
        width: 150px !important;
    }

    .ph-w-180px {
        width: 180px !important;
    }

    .ph-w-190px {
        width: 190px !important;
    }

    .ph-w-200 {
        width: 200px !important;
    }

    .ph-w-220px {
        width: 220px !important;
    }

    .ph-w-230px {
        width: 230px !important;
    }

    .ph-w-256 {
        width: 256px !important;
    }

    .ph-w-300 {
        width: 300px !important;
    }

    .ph-w-330 {
        width: 330px !important;
    }

    .ph-w-360 {
        width: 370px !important;
    }

    .ph-min-w-200 {
        min-width: 200px !important;
    }

    .ph-min-w-300 {
        min-width: 300px !important;
    }

    .ph-h-unset {
        height: unset !important;
    }

    .ph-h-30px {
        height: 30px !important;
    }

    .ph-h-75px {
        height: 75px !important;
    }

    .ph-h-130 {
        height: 130px !important;
    }

    .ph-w-100-20 {
        width: calc(100vw - 20px) !important;
    }

    .ph-w-100-30 {
        width: calc(100vw - 30px) !important;
    }

    .ph-w-100-40 {
        width: calc(100vw - 40px) !important;
    }

    .ph-w-100-45 {
        width: calc(100vw - 45px) !important;
    }

    .ph-w-100-50 {
        width: calc(100vw - 50px) !important;
    }

    .ph-w-100-60 {
        width: calc(100vw - 60px) !important;
    }

    .ph-w-100-80 {
        width: calc(100vw - 80px) !important;
    }

    .ph-w-100-135 {
        width: calc(100vw - 135px) !important;
    }

    .ph-w-100-150 {
        width: calc(100vw - 150px) !important;
    }

    .ph-w-100-160 {
        width: calc(100vw - 160px) !important;
    }

    .ph-w-100-165 {
        width: calc(100vw - 165px) !important;
    }

    .ph-w-100-180 {
        width: calc(100vw - 180px) !important;
    }

    .ph-w-100-190 {
        width: calc(100vw - 190px) !important;
    }

    .ph-w-100-210 {
        width: calc(100vw - 210px) !important;
    }

    // using
    .ph-h-100-50 {
        height: calc(100dvh - 50px) !important;
    }

    .ph-h-100-130 {
        height: calc(100dvh - 130px) !important;
    }

    .ph-h-100-185 {
        height: calc(100dvh - 185px) !important;
    }

    .ph-h-100-200 {
        height: calc(100dvh - 200px) !important;
    }

    .ph-h-100-220 {
        height: calc(100dvh - 220px) !important;
    }

    .ph-h-100-275 {
        height: calc(100dvh - 275px) !important;
    }

    .ph-h-100-340 {
        height: calc(100dvh - 340px) !important;
    }

    .ph-h-100-350 {
        height: calc(100dvh - 350px) !important;
    }

    .ph-h-100-358 {
        height: calc(100dvh - 358px) !important;
    }

    .ph-h-100-385 {
        height: calc(100dvh - 385px) !important;
    }

    //need to check dashboard v2
    .ph-h-100-380 {
        height: calc(100dvh - 380px) !important;
    }

    .ph-max-h-100-525 {
        max-height: calc(100vh - 525px) !important;
    }

    .ph-mx-4 {
        margin-left: 4px !important;
        margin-right: 4px !important;
    }

    .ph-mt-0 {
        margin-top: 0 !important;
    }

    .ph-mt-4 {
        margin-top: 4px !important;
    }

    .ph-mt-6 {
        margin-top: 6px !important;
    }

    .ph-mt-10 {
        margin-top: 10px !important;
    }

    .ph-mr-16 {
        margin-right: 16px !important;
    }

    .ph-mt-20 {
        margin-top: 20px !important;
    }

    .ph-mr-0 {
        margin-right: 0px !important;
    }

    .ph-mr-4 {
        margin-right: 4px !important;
    }

    .ph-mr-10 {
        margin-right: 10px !important;
    }

    .ph-mr-12 {
        margin-right: 12px !important;
    }

    .ph-mr-20 {
        margin-right: 20px !important;
    }

    .ph-mb-4 {
        margin-bottom: 4px;
    }

    .ph-mb-10 {
        margin-bottom: 10px !important;
    }

    .ph-mb-16 {
        margin-bottom: 16px;
    }

    .ph-mb-20 {
        margin-bottom: 20px !important;
    }

    .ph-mb-80 {
        margin-bottom: 80px !important;
    }

    .ph-ml-0 {
        margin-left: 0px !important;
    }

    .ph-ml-10 {
        margin-left: 10px !important;
    }

    .ph-ml-16 {
        margin-left: 16px !important;
    }

    .ph-ml-20 {
        margin-left: 20px !important;
    }

    .ph-ml-25 {
        margin-left: 25px !important;
    }

    .ph-mx-0 {
        margin-left: 0px !important;
        margin-right: 0px !important;
    }

    .ph-mx-10 {
        margin-left: 10px !important;
        margin-right: 10px !important;
    }

    .ph-mx-16 {
        margin-left: 16px !important;
        margin-right: 16px !important;
    }

    .ph-p-10 {
        padding: 10px !important;
    }

    .ph-p-20 {
        padding: 20px !important;
    }

    .ph-px-4 {
        padding-left: 4px !important;
        padding-right: 4px !important;
    }

    .ph-px-10 {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    .ph-px-16 {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }

    .ph-pt-40 {
        padding-top: 40px !important;
    }

    .ph-pr-0 {
        padding-right: 0px !important;
    }

    .ph-pb-10 {
        padding-bottom: 10px;
    }

    .ph-pl-10 {
        padding-left: 10px !important;
    }

    .ph-pl-16 {
        padding-left: 16px !important;
    }

    .ph-pl-20 {
        padding-left: 20px !important;
    }

    .ph-d-none {
        display: none !important;
    }

    .ph-d-block {
        display: block !important;
    }

    .ph-d-flex {
        display: flex !important;
        flex-direction: row !important;
    }

    .ph-flex-col {
        flex-direction: column;
    }

    .ph-flex-center-col {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .ph-flex-wrap {
        flex-wrap: wrap;
    }

    .ph-flex-start {
        align-items: flex-start !important;
    }

    .ph-flex-end {
        align-items: flex-end !important;
        justify-content: end !important;
    }

    .ph-flex-between-unset {
        align-items: unset !important;
        justify-content: unset !important;
    }

    .ph-justify-between-unset {
        display: unset !important;
        justify-content: unset !important;
    }

    .ph-flex-center {
        justify-content: center !important;
        align-items: center !important;
    }

    .ph-text-center {
        text-align: center !important;
    }

    .ph-flex-grow-1 {
        flex-grow: 1 !important;
    }

    .ph-flex-grow-unset {
        flex-grow: unset !important;
    }

    .ph-w-unset {
        width: unset !important;
    }

    .ph-modal-unset {
        width: unset !important;
        min-width: unset !important;
    }

    .ph-header-1 {
        font-size: $base-font-size + 13px;
        line-height: $base-font-size + 17px;
    }

    .ph-header-3 {
        font-size: $base-font-size + 3px !important;
        line-height: $base-font-size + 9px;
    }

    .ph-text-xl {
        font-size: $base-font-size + 3px !important;
        line-height: $base-font-size + 7px;
    }

    .ph-position-relative {
        position: relative !important;
    }

    .ph-position-absolute {
        position: absolute !important;
    }

    .ph-bottom-0 {
        bottom: 0 !important;
    }

    .ph-nleft-65 {
        left: -65px !important;
    }

    .ph-ntop-8 {
        top: -8px !important;
    }

    .ph-ntop-12 {
        top: -12px !important;
    }

    .ph-top-30 {
        top: 30% !important;
    }

    .ph-right-0 {
        right: 0px !important;
    }

    .ph-right-10 {
        right: 10px !important;
    }

    .ph-right-20 {
        right: 20px !important;
    }

    .ph-right-40 {
        right: 40px !important;
    }

    .ph-bottom-50 {
        bottom: 50px !important;
    }


    .ph-left-0 {
        left: 0px !important;
    }

    .ph-left-12 {
        left: 12px !important;
    }

    .ph-left-100 {
        left: 100px !important;
    }

    .ph-left-150 {
        left: 150px !important;
    }

    .ph-left-45 {
        left: 45% !important;
    }

    .ph-br-0 {
        border-radius: 0 !important;
    }

    .ph-br-mud {
        border-bottom: 1px solid $dark-700 !important;
    }

    .ph-border-bottom {
        border-bottom: 1px solid $dark-400;
    }

    .ph-border-0 {
        border: 0!important;
    }
}