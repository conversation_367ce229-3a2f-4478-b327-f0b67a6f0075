<div class="w-100">
  <div class="justify-between h-20px">
    <div *ngIf="leadTags?.length" class="align-center mt-2 text-nowrap" (click)="openLeadPreviewModal($event)">
      <ng-container *ngFor="let tag of leadTags">
        <div class="flex-center mr-4">
          <img [type]="'leadrat'" [appImage]="tag?.flag?.activeImagePath" class="w-10 h-10 obj-fill" [title]="tag?.flag?.name" />
        </div>
      </ng-container>
      <ng-container *ngIf="showRemainingTags">
        <div (click)="showAllTags($event)" class="h-10 flex-center" [title]="allLeadTags?.length + ' Total Flags'">
          +{{ remainingTags?.length }}
        </div>
      </ng-container>
    </div>
    <!-- <marquee direction="right" behavior="scroll" onmouseover="stop();" onmouseout="start();"> -->
    <div class="align-center text-white text-xxs right-0 position-relative text-truncate">
      <div *ngIf="params.data?.isConvertedFromData"
        class="ml-4 py-4 px-8 bg-blue-1100 text-nowrap cursor-pointer brbr-4 brbl-4"
        title="Data Converted: This lead was converted from a data.">DC</div>
      <div [title]="params.data?.meetingsDone?.length + ' Meeting Done'" *ngIf="params.data?.meetingsDone?.length"
        class="ml-4 py-4 px-8 bg-green-70 text-nowrap cursor-pointer brbr-4 brbl-4"
        (click)="openLocationModal($event, true)">
        {{ "LEAD_FORM.meetings-done" | translate }} -
        {{ params.data?.meetingsDone?.length }}</div>
      <div [title]="params.data?.meetingsNotDone?.length + ' Meeting Not Done'"
        *ngIf="params.data?.meetingsNotDone?.length"
        class="ml-4 py-4 px-8 bg-red-70 text-nowrap cursor-pointer brbr-4 brbl-4">
        {{ "LEAD_FORM.meetings-not-done" | translate }} -
        {{ params.data?.meetingsNotDone?.length }}</div>
      <div [title]="params.data?.siteVisitsDone?.length + (globalSettingsData?.shouldRenameSiteVisitColumn ? ' Referral Taken' : ' Site Visit Done')"
        *ngIf="params.data?.siteVisitsDone?.length"
        class="ml-4 py-4 px-8 bg-green-70 text-nowrap cursor-pointer brbr-4 brbl-4" (click)="openLocationModal($event)">
        {{ globalSettingsData?.shouldRenameSiteVisitColumn ? "RT" : "LEAD_FORM.visits-done" | translate }} -
        {{ params.data?.siteVisitsDone?.length }}</div>
      <div [title]="
          params.data?.siteVisitsNotDone?.length + (globalSettingsData?.shouldRenameSiteVisitColumn ? ' Referral Not Taken' : ' Site Visit Not Done')"
        *ngIf="params.data?.siteVisitsNotDone?.length"
        class="ml-4 py-4 px-8 bg-red-70 text-nowrap cursor-pointer brbr-4 brbl-4">
        {{ globalSettingsData?.shouldRenameSiteVisitColumn ? "RNT" : "LEAD_FORM.visits-not-done" | translate }} -
        {{ params.data?.siteVisitsNotDone?.length }}</div>
    </div>
    <!-- </marquee> -->
  </div>
  <div class="align-center text-nowrap">
    <div [title]="params.value[0]" class="header-6 text-secondary text-truncate-1 break-all cursor-pointer nmb-4 lh-21px">
      {{ params.value[0] }}</div>
    <ng-container *ngIf="canViewDuplicateTag">
      <div [title]="'Total Duplicates: ' + params.data.childLeadsCount"
        class="text-light-orange fw-700 ml-6 header-6 mt-4 cursor-pointer"
        (click)="duplicateHandler($event,'TD'); $event.preventDefault(); $event.stopPropagation()"
        *ngIf="params.data.childLeadsCount">
        (TD-{{params.data.childLeadsCount}})
      </div>
      <div [title]="'Duplicate Lead Version :'  +params.data.duplicateLeadVersion"
        class="text-light-orange fw-700 ml-6 header-6 mt-4" *ngIf="params.data.duplicateLeadVersion"
        (click)="duplicateHandler($event,'D'); $event.preventDefault(); $event.stopPropagation()">
        ({{params.data.duplicateLeadVersion}})</div>
    </ng-container>
  </div>
  <h6 class="animated-text fw-700 my-4 text-red-450" *ngIf="!params.data?.isPicked">
    (Untouched)
  </h6>
</div>

<ng-template #allTagsModal>
  <div class="p-20">
    <div class="icon ic-close-secondary ic-large cursor-pointer position-absolute ntop-10 nright-30"
      (click)="modalService.hide()"></div>
    <h3 class="mb-16 fw-semi-bold">Tags</h3>
    <div class="flex-center flex-wrap mt-2">
      <ng-container *ngFor="let tag of allLeadTags">
        <div class="w-25 ip-w-33">
          <div class="flex-center-col mr-24 mb-10 tb-mr-10">
            <img [type]="'leadrat'" [appImage]="tag?.flag?.activeImagePath">
            <div class="text-truncate-1 break-all">{{ tag?.flag?.name }}</div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</ng-template>

<ng-template #confirmModal>
  <div class="p-20">
    <h3 class="text-black-100 fw-semi-bold mb-20 text-center">This Lead Duplicate is not available in Current Module.
      <br> would you like to navigate to the Lead Module.
    </h3>
    <div class="flex-end mt-30">
      <button class="btn-gray mr-20" (click)="isClickYesOrNo(false)" id="clkSettingsNo" data-automate-id="clkSettingsNo">
        Cancel</button>
      <button class="btn-green" (click)="isClickYesOrNo(true)" id="clkSettingsYes" data-automate-id="clkSettingsYes">
        Proceed</button>
    </div>
  </div>
</ng-template>