<div class="p-20">
  <div class="flex-between">
    <div class="align-center" *ngFor="let lType of leadHistoryCategory; let i = index">
      <div class="form-check form-check-inline align-center btn pl-0 mr-0">
        <input type="radio" id="inpLeadHistory{{ i }}" data-automate-id="inpLeadHistory" name="leadHistory"
          [checked]="lType.value === selectedLeadHistory" [value]="lType.value" class="radio-check-input mr-10"
          (change)="filterHistory(lType.value)" />
        <label class="fw-600 text-secondary cursor-pointer text-large" for="inpLeadHistory{{ i }}">
          {{ lType.dispName }}</label>
      </div>
    </div>
  </div>
  <div class="ph-h-100-340 scrollbar" [ngClass]="whatsAppComp ? 'h-100-440' : 'h-100-315'">
    <ng-container *ngIf="!leadHistoryIsLoading else ratLoader">
      <ul class="time-line has-left-content time-line-dashed mt-20 pb-0" *ngIf="filteredHistoryList else noHistory">
        <ng-container *ngFor="let item of filteredHistoryList">
          <li class="dot-orange">
            <p class="left-content text-xs">{{item.date | relativeDay}}</p>
            <ng-container *ngFor="let group of item.data">
              <ng-container *ngFor="let indHistory of group">
                <div *ngIf="indHistory[1].length" class="dot-gray"></div>
                <div *ngIf="indHistory[1].length">
                  <div class="justify-between my-10">
                    <p>{{ 'GLOBAL.done-by' | translate }}
                      <a class="fw-600"> {{ indHistory[1][0]?.updatedBy }}</a>
                    </p>
                    <span class="text-gray fw-600 text-nowrap text-xs mx-20">
                      {{ getTimeZoneDate(indHistory[1][0]?.updatedOn, userData?.timeZoneInfo?.baseUTcOffset,
                      'timeWithMeridiem') }}
                    </span>
                  </div>
                  <ng-container *ngFor="let history of indHistory[1]; let ind = index">
                    <div class="w-100 text-xs text-gray gap-1">
                      <ng-container [ngSwitch]="history?.fieldName">
                        <ng-container *ngSwitchCase="'Custom Flags'">
                          <div class="align-center gap-1">
                            <div class="gray-dot"></div>
                            <span class="fw-600">
                              {{ getChangeFlags(history?.oldValue, history?.newValue) }}
                            </span>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Rating'">
                          <div class="align-center gap-1">
                            <div class="gray-dot"></div>
                            {{ history?.fieldName }} updated
                            <span *ngIf="history?.oldValue != ''" class="fw-600">{{ history.oldValue }} Star(s)</span>
                            <h3 class="mb-2">&#x2192;</h3>
                            <a class="fw-600"> {{ history?.newValue }} Star(s)</a>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Scheduled Date'">
                          <div *ngIf="history?.newValue" class="align-center gap-1 my-4">
                            <span class="gray-dot"></span>
                            {{ history?.fieldName }} : <span class="fw-600">{{ getTimeZoneDate(history?.newValue,
                              userData?.timeZoneInfo?.baseUTcOffset, 'dateWithTime') }}</span>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Picked Date'">
                          <div *ngIf="history?.newValue" class="align-center gap-1 my-4">
                            <span class="gray-dot"></span>
                            {{ history?.fieldName }} : <span class="fw-600">{{ getTimeZoneDate(history?.newValue,
                              userData?.timeZoneInfo?.baseUTcOffset, 'dateWithTime') }} </span>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Booked Date'">
                          <div *ngIf="history?.newValue" class="align-center gap-1 my-4">
                            <span class="gray-dot"></span>
                            {{ history?.fieldName }} : <span class="fw-600">{{ getTimeZoneDate(history?.newValue,
                              userData?.timeZoneInfo?.baseUTcOffset, 'dateWithTime') }}</span>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Assigned To User'">
                          <div class="align-center gap-1">
                            <div class="gray-dot"></div>
                            {{ history?.fieldName + getAuditActionType(history?.auditActionType)}}
                            <span class="fw-600 align-center gap-1 mr-10 word-break line-break">{{history?.oldValue}}
                              <h3 class="mb-2">&#x2192;</h3>
                              {{ history?.newValue ? history?.newValue : 'unassigned' }}
                            </span>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Possession Date'">
                          <div class="align-center gap-1">
                            <div class="gray-dot"></div>
                            {{ 'Possession Needed By' + getAuditActionType(history?.auditActionType) }}
                            <span class="fw-600 align-center gap-1 mr-10 word-break line-break">
                              {{ history?.oldValue ? getTimeZoneDate( history?.oldValue,
                              userData?.timeZoneInfo?.baseUTcOffset,'dayMonthYear'): ''}}
                              <h3 class="mb-2">&#x2192;</h3>{{history?.newValue ?
                              getTimeZoneDate( history?.newValue, userData?.timeZoneInfo?.baseUTcOffset,'dayMonthYear')
                              :
                              ''}}
                            </span>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Documents'">
                          <div class="align-center gap-1">
                            <div class="gray-dot"></div>
                            <ng-container *ngIf="!history?.oldValue">
                              {{'LEADS.added-document' | translate}} <span class="header-3 mb-2">&#x2192;</span>
                              <span class="fw-700 word-break line-break max-w-100px">{{ history?.newValue }}</span>
                            </ng-container>
                            <ng-container *ngIf="!history?.newValue">
                              {{'LEADS.deleted-document' | translate}}<span class="header-3 mb-2">&#x2192;</span>
                              <span class="fw-700 word-break line-break">{{ history?.oldValue }}</span>
                            </ng-container>
                            <ng-container *ngIf="history?.newValue && history?.oldValue">
                              {{'LEADS.updated-document-list' | translate}}
                              <span class="fw-700 word-break line-break max-w-100px">{{ history?.oldValue }}</span>
                              <span class="header-3 mb-2">&#x2192;</span>
                              <span class="fw-700 word-break line-break max-w-100px">{{ history?.newValue }}</span>
                            </ng-container>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Contact Records'">
                          <p class="gray-card">
                            {{'LEADS.contacted-through' | translate}}
                            <span class="fw-700 word-break line-break max-w-100px">{{ history?.newValue }}</span>
                          </p>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Is Meeting Done'">
                          <div class="align-center gap-1 fw-700">
                            <div class="gray-dot"></div>
                            <p>{{( history?.newValue === 'True' ? 'LEADS.meeting-done' :
                              'LEAD_FORM.meeting-not-done' ) | translate}}</p>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Is Site Visit Done'">
                          <div class="align-center gap-1 fw-700">
                            <div class="gray-dot"></div>
                            <ng-container *ngIf="globalSettingsDetails?.shouldRenameSiteVisitColumn; else defaultTexts">
                              <p>{{ history?.newValue === 'True' ? 'Referral Taken' : 'Referral Not Taken' }}</p>
                            </ng-container>
                            <ng-template #defaultTexts>
                              <p>{{ history?.newValue === 'True' ? 'LEADS.site-visit-done' : 'LEAD_FORM.visit-not-done' }}</p>
                            </ng-template>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Meeting Location'">
                          <ng-container
                            *ngIf="history?.newValue && history?.newValue != 'null' && history?.newValue != null">
                            <p class="gray-card">{{ 'LEAD_FORM.meeting' | translate }}
                              {{ 'LOCATION.location' | translate }} : <a class="fw-700">
                                {{ getAddress(history?.newValue) }}</a>
                              <a class="d-block mt-10 text-decoration-underline"
                                (click)="onViewOnMap(history?.newValue)">{{ 'LEAD_FORM.view-on-map' | translate }}</a>
                            </p>
                          </ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Site Location'">
                          <ng-container
                            *ngIf="history?.newValue && history?.newValue != 'null' && history?.newValue != null">
                            <p class="gray-card">{{'LEAD_FORM.site-visit' | translate
                              }}
                              {{'LOCATION.location' | translate }} :
                              <a class="fw-700">{{ getAddress(history?.newValue) }}</a>
                              <a class="d-block mt-10 text-decoration-underline"
                                (click)="onViewOnMap(history?.newValue)">{{ 'LEAD_FORM.view-on-map' | translate }}</a>
                            </p>
                          </ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Lead Appointment'">
                          <div class="gray-card gap-1" *ngIf="jsonFormat(history?.newValue)">
                            <p class="fw-700 mb-10">{{ history?.fieldName }} Updated</p>
                            <div *ngFor="let field of jsonFormat(history?.newValue)">
                              <span
                                *ngIf="!leadHiddenFields.includes(field[0]) && field[1] && field[0] !== 'ImagesWithName'">
                                {{ field[0] == 'ProjectName' ? 'Project Name' :
                                field[0] == 'ExecutiveName' ? 'Sales Executive Name' :
                                field[0] == 'ExecutiveContactNo' ? 'Sales Executive Number' :
                                field[0] }} -
                                <span class="fw-700"
                                  [innerHTML]="field[0] == 'Location' ? convertAndSanitize(getAddress(field[1])) : convertAndSanitize(field[1])">
                                </span>
                              </span>
                              <span class="align-center gap-1"
                                *ngIf="!leadHiddenFields.includes(field[0]) && field[1] && field[0] === 'ImagesWithName'">
                                <ng-container *ngIf="field[1].length">
                                  {{ 'LEAD_FORM.documents' | translate }} -
                                  <div *ngFor="let doc of field[1]; let last = last">
                                    {{ doc?.DocumentName }}<span *ngIf="!last">, </span>
                                  </div>
                                </ng-container>
                              </span>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'LeadCallLog'">
                          <div class="gray-card gap-1" *ngIf="LeadCallFormate(history?.newValue) as callData">
                            <p class="fw-700 mb-10">{{ 'Call Recordings'}}</p>
                            <div class="flex flex-col">
                              <div class="flex items-center gap-1">
                                <span class="fw-600">{{callData.callType}}</span>
                                <span class="mx-1">→</span>
                                <span class="fw-600">{{callData.status}}</span>
                                <span class="mx-1">→</span>
                                <span class="fw-600">{{callData.duration}}</span>
                              </div>
                              <div *ngIf="callData.url" class="">
                                <audio preload="auto" #audioPlayer controls (play)="pauseOtherAudio(audioPlayer)"
                                  (canplay)="isLoading = false" (loadedmetadata)="isLoading = false">
                                  <source [src]='callData.url ? decodeAudioUrl(callData.url) : null' type="audio/mp3">
                                </audio>
                                <div *ngIf="isLoading">
                                  <ng-lottie [options]="loader" width="30px" height="30px"
                                    class="position-absolute top-10 left-6">
                                  </ng-lottie>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Lead Communication'">
                          <div class="gray-card gap-1" *ngIf="jsonFormat(history?.newValue)">
                            <p class="fw-700 mb-10">{{ history?.fieldName }} Updated</p>
                            <div *ngFor="let field of jsonFormat(history?.newValue)">
                              <span *ngIf="!leadHiddenFields.includes(field[0]) && field[1]">
                                {{field[0]}} -
                                <div class="fw-700 text-sm break-word break-all"
                                  [innerHTML]="retrieveMessageFromBackend(field[1])">
                                </div>
                              </span>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Booked Details Information'">
                          <div class="gray-card gap-1" *ngIf="bookingFormHistory(history?.newValue)">
                            <p class="fw-700 mb-10">{{ history?.fieldName }} Updated</p>
                            <div *ngFor="let field of bookingFormHistory(history?.newValue) | keyvalue"
                              class="fw-600 align-center gap-1 mr-10 break-word line-break">
                              {{field.key}} <h3 class="mb-2">&#x2192;</h3> <span [innerHTML]="field.value"></span>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Documents Details Information'">
                          <div class="gray-card gap-1" *ngIf="documentsbookingFormHistory(history?.newValue)">
                            <p class="fw-700 mb-10">{{ history?.fieldName }} Updated</p>
                            <div *ngFor="let field of documentsbookingFormHistory(history?.newValue) | keyvalue"
                              class="fw-600 align-center gap-1 mr-10 break-word line-break">
                              {{field.key}} <h3 class="mb-2">&#x2192;</h3> {{field.value}}
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Lead Brokerage Details Information'">
                          <div class="gray-card gap-1" *ngIf="brockerageFormHistory(history?.newValue)">
                            <p class="fw-700 mb-10">{{ history?.fieldName }} Updated</p>
                            <div *ngFor="let field of brockerageFormHistory(history?.newValue) | keyvalue"
                              class="fw-600 align-center gap-1 mr-10 break-word line-break">
                              {{field.key}} <h3 class="mb-2">&#x2192;</h3> {{field.value}}
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'WhatsApp Communication'">
                          <div class="gray-card gap-1" *ngIf="jsonFormat(history?.newValue)">
                            <p class="fw-700 mb-10">{{ history?.fieldName }} Updated</p>
                            <div *ngFor="let field of jsonFormat(history?.newValue)">
                              <span *ngIf="!leadHiddenFields.includes(field[0]) && field[1]" class="d-flex gap-1">
                                WhatsApp Business -
                                <div class="fw-700 text-sm break-word break-all"
                                  [innerHTML]="retrieveMessageFromBackend(field[1])">
                                </div>
                              </span>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Is Archived'">
                          <div class="d-flex">
                            <p class="gray-card fw-700">
                              {{ (history?.newValue === 'True' ? 'LEADS.deleted' : 'LEADS.restored') | translate }}
                            </p>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Notes'">
                          <div class="position-relative" *ngIf="history?.newValue">
                            <div class="d-flex">
                              <p class="gray-card gap-1">
                                <span class="fw-700">{{ history?.fieldName }}: </span>
                                <span class="word-break line-break"
                                  [innerHTML]="convertUrlsToLinks(history?.newValue)"></span>
                              </p>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'BHKs'">
                          <div class="align-center gap-1">
                            <div class="gray-dot"></div>
                            <div class="fw-600 align-center gap-1 mr-10 word-break line-break">
                              <ng-container *ngIf="(history?.oldValue && !history?.newValue) else updatedData">
                                <span class="text-sm fw-400 my-4">{{history?.fieldName}}
                                  <span class="fw-600">({{getBHKDisplay(history?.oldValue)}})</span> has
                                  been
                                  removed</span>
                              </ng-container>
                              <ng-template #updatedData>
                                <span class="text-sm fw-400 align-center"> {{
                                  history?.fieldName + getAuditActionType(history?.auditActionType) }}</span>
                                {{getBHKDisplay(history?.oldValue)}} <h3>&#x2192;</h3>
                                {{getBHKDisplay(history?.newValue) }}</ng-template>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Beds'">
                          <div class="align-center gap-1">
                            <div class="gray-dot"></div>
                            <div class="fw-600 align-center gap-1 mr-10 word-break line-break">
                              <ng-container *ngIf="(history?.oldValue && !history?.newValue) else updatedData">
                                <span class="text-sm fw-400 my-4">{{history?.fieldName}}
                                  <span class="fw-600">({{getBedsDisplay(history?.oldValue)}})</span>
                                  has been removed</span>
                              </ng-container>
                              <ng-template #updatedData>
                                <span class="text-sm fw-400 align-center"> {{ history?.fieldName +
                                  getAuditActionType(history?.auditActionType) }}</span>
                                {{getBedsDisplay(history?.oldValue) || ''}} <h3>&#x2192;</h3>
                                {{ getBedsDisplay(history?.newValue) }}</ng-template>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Lead got added'">
                          <p class="bg-secondary p-10 br-5 max-w-390 fw-700 align-center">
                            {{ history?.fieldName }} <span class="header-3">🥳</span> </p>
                        </ng-container>
                        <ng-container *ngSwitchDefault>
                          <ng-container *ngIf="history?.fieldName?.startsWith('Is') else default">
                            <div class="align-center">
                              <div class="gray-dot"></div>
                              {{ ( history?.newValue === 'True' ? 'LEADS.lead-flagged' : 'LEADS.lead-unflagged' ) |
                              translate }}
                              <span class="fw-700 ml-2 text-sm" *ngFor="let word of history?.fieldName?.split(' ')">
                                {{word!=='Is' ? word : ''}}
                              </span>
                            </div>
                          </ng-container>
                          <ng-template #default>
                            <div *ngIf="!hides?.includes(history?.fieldName)" class="align-center gap-1">
                              <ng-container *ngIf="(history?.oldValue && !history?.newValue) else updatedData">
                                <div class="gray-dot"></div>
                                <span class="text-sm fw-400 my-4" *ngIf="history?.fieldName !== 'Scheduled Date'"> {{
                                  history?.fieldName }}
                                  <span class="fw-600">({{history?.oldValue?.trim()}})</span>
                                  has been removed
                                </span>
                              </ng-container>
                              <ng-template #updatedData>
                                <ng-container [ngSwitch]="history?.fieldName">
                                  <ng-container *ngIf="canViewLeadSource">
                                    <ng-container *ngSwitchCase="'Lead Source'">
                                      <div class="gray-dot"></div>
                                      <div class="fw-600 align-center gap-1 mr-10 break-word line-break">
                                        <span class="text-sm fw-400"> {{ history?.fieldName + ' updated ' }}</span>
                                        {{history?.oldValue }} <h3 class="mb-2">&#x2192;</h3> {{ history?.newValue }}
                                      </div>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'Sub Source'">
                                      <div class="gray-dot"></div>
                                      <div class="fw-600 align-center gap-1 mr-10 break-word line-break">
                                        <span class="text-sm fw-400"> {{ history?.fieldName + ' updated ' }}</span>
                                        {{history?.oldValue }} <h3 class="mb-2">&#x2192;</h3> {{ history?.newValue }}
                                      </div>
                                    </ng-container>
                                  </ng-container>
                                  <ng-container
                                    *ngIf="!leadHiddenItems.includes(history?.fieldName)  && history?.fieldName !== 'Sub Source' && history?.fieldName !== 'Lead Source'">
                                    <div class="gray-dot"></div>
                                    <div class="fw-600 align-center gap-1 mr-10 break-word line-break">
                                      <span class="text-sm fw-400">{{history?.fieldName +
                                        getAuditActionType(history?.auditActionType)}}</span>
                                      <span [innerHTML]="convertUrlsToLinks(history?.oldValue === 'Site Visit Scheduled' ? (globalSettingsDetails?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : history?.oldValue) : history?.oldValue)"></span>
                                      <h3 class="mb-2">&#x2192;</h3>
                                      <span [innerHTML]="convertUrlsToLinks(history?.newValue === 'Site Visit Scheduled' ? (globalSettingsDetails?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : history?.newValue) : history?.newValue)"></span>
                                    </div>
                                  </ng-container>
                                </ng-container>
                              </ng-template>
                            </div>
                          </ng-template>
                        </ng-container>
                      </ng-container>
                    </div>
                  </ng-container>
                </div>
              </ng-container>
            </ng-container>
          </li>
        </ng-container>
      </ul>
    </ng-container>
    <ng-template #noHistory>
      <div class="h-100-393 flex-center-col">
        <ng-lottie [options]='noDocument' width="200px" height="200px"></ng-lottie>
        <div class="fw-600 header-4 text-light-slate">{{ 'LEADS.no-documents' | translate }}</div>
      </div>
    </ng-template>

    <ng-template #ratLoader>
      <div class="flex-center w-100 h-100">
        <img src="assets/images/loader-rat.svg" class="rat-loader" alt="loader">
      </div>
    </ng-template>
  </div>
</div>