<div class="h-100vh text-coal">
  <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
    <h3 class="fw-semi-bold">Send {{data?.shareType}} message</h3>
    <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
  </div>
  <div class="px-20 scrollbar h-100-100 bg-white">
    <div class="bg-secondary br-4 mt-16 px-10 pt-4 pb-10">
      <div class="align-center mt-10">
        <div class="field-label m-0 fw-600 text-nowrap">{{(data?.isData ? 'CONTACT.data' : 'GLOBAL.lead') | translate }}:
        </div>
        <div class="ml-6 text-truncate-2 break-all">{{data.name | titlecase}}</div>
      </div>
    </div>
    <div *ngIf="data.alternateContactNo && (data?.shareType == 'WhatsApp' || data?.shareType == 'SMS')">
      <h5 class="field-label">choose which number you want to <span class="text-lowercase">{{data?.shareType}}?</span>
      </h5>
      <div class="d-flex">
        <div class="align-center">
          <div class="form-check form-check-inline" (click)="selectedCallType = data?.contactNo">
            <input type="radio" id="inpWhatsappPhoneNumber" data-automate-id="inpWhatsappPhoneNumber"
              class="radio-check-input" name="callType" checked>
            <label class="fw-600 text-secondary cursor-pointer text-large" for="inpWhatsappPhoneNumber">
              Primary
            </label>
          </div>
        </div>
        <div class="align-center">
          <div class="form-check form-check-inline" (click)="selectedCallType = data?.alternateContactNo">
            <input type="radio" id="inpWhatsappAltPhoneNumber" data-automate-id="inpWhatsappAltPhoneNumber"
              class="radio-check-input" name="callType">
            <label class="fw-600 text-secondary cursor-pointer text-large" for="inpWhatsappAltPhoneNumber">
              {{'GLOBAL.alternate' | translate}}
            </label>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="data?.shareType == 'Email'">
      <h5 class="field-label">{{data?.shareType}}</h5>
      <div class="bg-secondary p-12 br-5 text-truncate">{{data.email ? data?.email : '--'}}</div>
    </div>
    <div class="d-flex">
      <div class="align-center border br-20 mt-6 p-6 mb-2">
        <div [ngClass]="{'bg-black text-white': selectedTab === 'lead'}" class="px-10 py-4 br-20 cursor-pointer">
          <span (click)="selectTab('lead')">
            Lead
          </span>
        </div>
        <div class="px-10 py-4 br-20 cursor-pointer"
          [ngClass]="{'bg-black text-white': selectedTab === 'project'}">
          <span (click)="selectTab('project')">
            Project
          </span>
        </div>
        <div class="px-10 py-4 br-20 cursor-pointer"
          [ngClass]="{'bg-black text-white': selectedTab === 'property'}">
          <span (click)="selectTab('property')">
            Property
          </span>
        </div>
      </div>
    </div>
    <div *ngIf="selectedTab === 'project'" class=" ng-select-sm">
      <div for="projectsList" class="field-label">{{ 'Select Project' }}</div>
      <ng-select [clearSearchOnAdd]="true"
        [items]="projectList"
        [ngClass]="{'blinking pe-none': projectListIsLoading}"
        [multiple]="true"
        [closeOnSelect]="false"
        ResizableDropdown
        bindLabel="name"
        bindValue="id"
        name="projectsList"
        placeholder="Select Project"
        (change)="onProjectSelect($event)"
        [(ngModel)]="selectedProjects">
        <ng-template ng-label-tmp let-item="item" let-clear="clear">
          <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
          <span class="ng-value-label">{{item.name}}</span>
        </ng-template>
        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
          <div class="flex-between">
            <div class="checkbox-container">
              <input type="checkbox" id="project-{{index}}" data-automate-id="project-{{index}}" [checked]="item$.selected">
              <span class="checkmark"></span>
              <span class="text-truncate-1 break-all">{{item.name}}</span>
            </div>
          </div>
        </ng-template>
      </ng-select>
    </div>

    <div *ngIf="selectedTab === 'property'" class="ng-select-sm">
      <div for="propertiesList" class="field-label">{{ 'Select Property' }}</div>
      <ng-select [virtualScroll]="true"
        [items]="propertyList"
        [multiple]="true"
        [closeOnSelect]="false"
        ResizableDropdown
        [ngClass]="{'blinking pe-none': propertyListIsLoading}"
        bindLabel="title"
        bindValue="id"
        name="propertiesList"
        placeholder="Select Property"
        (change)="onPropertySelect($event)"
        [(ngModel)]="selectedProperties">
        <ng-template ng-label-tmp let-item="item" let-clear="clear">
          <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
          <span class="ng-value-label">{{item.title}}</span>
        </ng-template>
        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
          <div class="flex-between">
            <div class="checkbox-container">
              <input type="checkbox" id="property-{{index}}" data-automate-id="property-{{index}}" [checked]="item$.selected">
              <span class="checkmark"></span>
              <span class="text-truncate-1 break-all">{{item.title}}</span>
            </div>
          </div>
        </ng-template>
      </ng-select>
    </div>

    <div *ngIf="selectedTab === 'lead' || (selectedTab !== 'lead' && isSelectedProjectProperty)">
      <div for="templateSelect" class="field-label">{{ 'Select Template' }}</div>
      <ng-select [ngClass]="{'pe-none blinking': (isTemplatesLoading || isTenantNameLoading)}" [virtualScroll]="true"
        ResizableDropdown [items]="templates" bindLabel="title" [(ngModel)]="selectedTemplate"
        placeholder="{{ 'GLOBAL.select' | translate }} {{ 'BULK_LEAD.template' | translate }}" id="templateSelect">
      </ng-select>
    </div>


    <div class="field-label">{{ 'LEADS.message'| translate }}</div>
    <div class="form-group">
      <textarea rows="8" [value]="getTextareaValue()" [(ngModel)]="message" class="scrollbar" id="txtLeadMsg"
        data-automate-id="txtLeadMsg" placeholder="ex. Dear Nichola Ferrell,As per Our Telephonic Discussion your Site Visit is Confirm. This is a Reminder Message Please Confirm the time sir.

Thankyou

Regards,
Vikram Surelia"></textarea>
    </div>
  </div>
  <button class="btn-coal w-260 mx-20" (click)="sendMessage()">{{ 'LEADS.send-message' | translate }}</button>
</div>