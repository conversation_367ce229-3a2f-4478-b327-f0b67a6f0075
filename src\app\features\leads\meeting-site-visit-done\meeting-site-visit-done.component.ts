import { Component, EventEmitter, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { MapInfoWindow } from '@angular/google-maps';
import { logError } from '@ckeditor/ckeditor5-utils';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { EMPTY_GUID } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getAssignedToDetails, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'meeting-site-visit-done',
  templateUrl: './meeting-site-visit-done.component.html',
})
export class MeetingSiteVisitDoneComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild(MapInfoWindow) infoWindow: MapInfoWindow;
  moment = moment;
  latitude: number = 12.8898194;
  longitude: number = 77.64237;
  markers: any[] = [];
  data: [];
  isMeeting: boolean;
  time: string[] = [];
  label = 1;
  center = {
    lat: 12.9106262,
    lng: 77.6405173,
  };
  selectedAddress: string;
  userList: any[] = [];
  s3BucketPath: string = env.s3ImageBucketURL;
  EMPTY_GUID = EMPTY_GUID;
  getAssignedToDetails = getAssignedToDetails;
  getTimeZoneDate = getTimeZoneDate;
  userData: any;
  globalSettingsData: any;
  constructor(
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    private _store: Store<AppState>
  ) {
    this._store.dispatch(new FetchUsersListForReassignment());
  }

  ngOnInit(): void {
    const sortedData = [...this.data].sort((a: any, b: any) => {
      const dateA = new Date(a?.lastModifiedOn);
      const dateB = new Date(b?.lastModifiedOn);
      return dateB.getTime() - dateA.getTime();
    });

    this._store.select(getGlobalSettingsAnonymous)
    .pipe(takeUntil(this.stopper))
    .subscribe((data: any) => {
      this.globalSettingsData = data;
    });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });

      sortedData?.map((item: any) => {
        const date = new Date(item?.lastModifiedOn);
        const formattedDate = date ? getTimeZoneDate(date, this.userData?.timeZoneInfo?.baseUTcOffset) : '';
        this.time.push(formattedDate);
        if (item?.location?.latitude > 0 && item?.location?.longitude > 0) {
          this.markers.push({
            latitude: Number(item?.location?.latitude),
            longitude: Number(item?.location?.longitude),
            label: String(this.label++),
            address: this.getLocationAddress(item?.location),
          });
        }
        
        else if (item?.location?.placeId) {          
          this.getCoordinatesFromPlaceId(item?.location?.placeId).then((coords) => {
            if (coords) {
              this.markers.push({
                latitude: Number(coords.lat),
                longitude: Number(coords.lng),
                label: String(this.label++),
                address: this.getLocationAddress(item?.location),
              });
            }
            
          });
        }
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => this.userList = data);
  }

  openInfoWindow(marker: any) {
    this.infoWindow.open(marker);
  }

  closeModal() {
    this.modalRef.hide();
  }

  showLocation(item: any, label: any) {
    if (item?.location) {
      if (item?.location?.latitude > 0 && item?.location?.longitude > 0) {
        this.markers[label - 1] = {
          latitude: Number(item?.location?.latitude),
          longitude: Number(item?.location?.longitude),
          label: String(label),
          address: this.getLocationAddress(item?.location),
        };
        this.center = {
          lat: Number(item?.location?.latitude),
          lng: Number(item?.location?.longitude),
        };
        this.selectedAddress = this.markers[label - 1]?.address;
      } else if (item?.location?.placeId) {
        this.getCoordinatesFromPlaceId(item?.location?.placeId).then((coords) => {
          if (coords) {
            this.markers[label - 1] = {
              latitude: Number(coords.lat),
              longitude: Number(coords.lng),
              label: String(label),
              address: this.getLocationAddress(item?.location),
            };
            this.center = {
              lat: coords.lat,
              lng: coords.lng,
            };
            this.selectedAddress = this.markers[label - 1]?.address;
          }
        })
      } 
    }
  }
 
  getLocationAddress(location: any): string {
    const subLocality = location?.subLocality;
    const city = location?.city;

    if (subLocality && city) {
      return `${subLocality}, ${city}`;
    } else if (city) {
      return city;
    } else if (subLocality) {
      return subLocality;
    } else {
      return 'Location not available';
    }
  }

  getCoordinatesFromPlaceId(placeId: string): Promise<{ lat: number, lng: number }> {
    return new Promise((resolve, reject) => {
      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ placeId }, (results, status) => {
        if (status === google.maps.GeocoderStatus.OK && results[0]?.geometry?.location) {
          resolve({
            lat: results[0].geometry.location.lat(),
            lng: results[0].geometry.location.lng(),
          });     
        } else {
          reject(`Geocode failed for placeId: ${placeId} - Status: ${status}`);
        }
      });
    });
  }
  

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}

