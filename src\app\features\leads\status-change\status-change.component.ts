import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { BehaviorSubject, combineLatest, firstValueFrom, Subject, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  skipWhile,
  take,
  takeUntil,
} from 'rxjs/operators';

import {
  EMPTY_GUID,
  LEAD_STATUS_REASONS,
  UPDATE_STATUS,
  UPDATE_STATUS_PAST_TENSE,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import { LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  formatBudget,
  getTimeZoneDate,
  onlyNumbers,
  onPickerOpened,
  patchFormControlValue,
  patchTimeZoneWithTime,
  setTimeZoneDateWithTime,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { BookingFormComponent } from 'src/app/features/leads/booking-form/booking-form.component';
import { LeadAppointmentComponent } from 'src/app/features/leads/lead-appointment/lead-appointment.component';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchInvoiceById } from 'src/app/reducers/invoice/invoice.actions';
import { getBookingData } from 'src/app/reducers/invoice/invoice.reducer';
import {
  FetchBulkOperation,
  UpdateLeadStatus,
  UpdateMultipleLead,
} from 'src/app/reducers/lead/lead.actions';
import {
  getLeadStatusIsLoading,
  getMultipleLeadStatusIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { LeadPreviewChanged } from 'src/app/reducers/loader/loader.actions';
import { FetchLeadStatusList } from 'src/app/reducers/master-data/master-data.actions';
import {
  getStatusIsLoading,
  getStatusMasterData,
} from 'src/app/reducers/master-data/master-data.reducer';
import {
  getPermissions,
  getPermissionsIsLoading,
} from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchProjectById,
  FetchProjectIdWithName,
} from 'src/app/reducers/project/project.action';
import {
  getIsProjectByIdLoading,
  getProjectsIDWithName,
  getProjectsIDWithNameIsLoading,
  getSelectedProjectById,
  getUnitInfo,
} from 'src/app/reducers/project/project.reducer';
import {
  FetchPropertyById,
  FetchPropertyWithIdNameList,
} from 'src/app/reducers/property/property.actions';
import {
  getPropertyListDetails,
  getPropertyWithIdLoading,
  getPropertyWithIdNameList,
} from 'src/app/reducers/property/property.reducer';
import { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';
import { getLocationsWithGoogleApi } from 'src/app/reducers/site/site.reducer';
import { FetchReportingManagerDetails } from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getAdminsAndReporteesIsLoading,
  getManagerDetails,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BulkOperationTrackerComponent } from 'src/app/shared/components/bulk-operation-tracker/bulk-operation-tracker.component';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';
@Component({
  selector: 'status-change',
  templateUrl: './status-change.component.html',
})
export class StatusChangeComponent
  implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  private onProjectSelect$: Subject<void> = new Subject<void>();
  private onPropertySelect$: Subject<void> = new Subject<void>();
  private isProjectSubscription: Subscription;
  getMiniBookingFormDatas: Subscription;
  @ViewChild('noUnitFound') noUnitFound: TemplateRef<any>;
  @Input() leadInfo: any;
  @Input() canShowStatusPopupInPreview: boolean = false;
  @Input() canUpdateStatus: boolean = false;
  @Input() isLeadPreview: boolean = false;
  @Input() isLastLead: boolean = false;
  @Input() closeLeadPreviewModal: any;
  @Input() whatsAppComp: boolean = false;
  updateForm: FormGroup;
  params: any;
  dispSubStatus: any = LEAD_STATUS_REASONS;
  leadStatus: any = UPDATE_STATUS;
  currentLeadStatus: any = UPDATE_STATUS_PAST_TENSE;
  updateLeadStates: any[] = [];
  callBackReason: any[] = [];
  notInterestedReason: any[] = [];
  placesList: any[] = [];
  minDate: Date;
  maxDate: Date;
  allLeadStatus: any;
  leadAuditHistory: any;
  selectedStatus: string;
  reasons: Array<string>;
  conditionalStatus: string[];
  selectedReason: string;
  defaultCurrency: string = '';
  budgetInWords: string = '';
  leadSource: string;
  hideStatus: boolean = false;
  isVisitDone: boolean = false;
  didCustDenyOtp: boolean = false;
  isBulkUpdate: boolean = false;
  disableStatus: boolean;
  showLocationSearch: boolean = false;
  projectList: Array<any> = [];
  propertyList: Array<any> = [];
  currencyList: any[] = [];
  leadStatusIsLoading: boolean = false;
  multipleLeadsIsLoading: boolean = false;
  propertyListIsLoading: boolean = true;
  projectListIsLoading: boolean = true;
  isNotesMandatory: boolean;
  elementHeight: any;
  assignedToUserId: string;
  canAssignLead: boolean = false;
  primaryUserList: Array<Object> = [];
  secondaryUserList: Array<Object> = [];
  primaryAgentList: Array<Object> = [];
  secondaryAgentList: Array<Object> = [];
  isUserListLoading: boolean = true;
  deactiveUsers: Array<Object> = [];
  isLeadStatusLoading: boolean = false;
  canUpdateBookedLead: boolean = false;
  isDualOwnershipEnabled: boolean;
  isReadMore: boolean = false;
  manualLocationsList: any[] = [];
  @ViewChild('statusForm') statusForm: ElementRef;
  @ViewChild('trackerInfoModal') trackerInfoModal: any;

  EMPTY_GUID = EMPTY_GUID;
  moment = moment;
  patchFormControlValue = patchFormControlValue;
  formatBudget = formatBudget;
  onlyNumbers = onlyNumbers;

  selectedFromTab: boolean = true;
  unitInfo: any;
  isShowBookingFormBtn: boolean = false;
  userList: any;
  isUploading: boolean = false;
  miniagreementValueInWords: any;
  currentPath: string;
  selectedProject: any = {};
  userDetails: any;
  togleBooCancelBtn: boolean = false;
  isShowUnitInfoField: boolean = false;
  isUnitInfoDataLoading: boolean;
  currency: any[] = [];
  isSelectedPropertyOrProject: string;
  currentDate: Date = new Date();
  userBasicDetails: any;
  getTimeZoneDate = getTimeZoneDate;
  onPickerOpened = onPickerOpened;
  isProjectMandatory: boolean = false;
  isPastDateSelectionEnabled: boolean = true;
  globalSettingsData: any;

  get canShowStatus(): boolean {
    return !this.hideStatus && !this.canShowStatusPopupInPreview;
  }

  get isUnassignedLead(): boolean {
    return this.leadInfo?.assignTo == EMPTY_GUID;
  }

  isSelectedOnlySomeBooked(): boolean {
    if (!this.leadInfo?.length) {
      return false;
    }

    const allBookedOrInvoiced = this.leadInfo.every(
      (lead: { status: { displayName: string } }) =>
        lead.status.displayName === 'Booked' ||
        lead.status.displayName === 'Invoiced'
    );

    const someBookedOrInvoiced = this.leadInfo.some(
      (lead: { status: { displayName: string } }) =>
        lead.status.displayName === 'Booked' ||
        lead.status.displayName === 'Invoiced'
    );

    return (
      allBookedOrInvoiced || (someBookedOrInvoiced && !allBookedOrInvoiced)
    );
  }

  isSelectedAllBookedOrInvoicedLead(): boolean {
    return this.leadInfo?.every(
      (lead: { status: { displayName: string } }) =>
        lead?.status?.displayName === 'Booked' ||
        lead?.status?.displayName === 'Invoiced'
    );
  }

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private formBuilder: FormBuilder,
    private _store: Store<AppState>,
    private cdr: ChangeDetectorRef,
    private _leadPreviewComponent: LeadPreviewComponent,
    private _notificationsService: NotificationsService,
    private router: Router,
    private shareDataService: ShareDataService,
    public trackingService: TrackingService
  ) {
    this.maxDate = new Date();
    // this.minDate = new Date();
    // this.minDate.setDate(this.minDate.getDate());
    this._store.dispatch(new FetchPropertyWithIdNameList());
    this._store.dispatch(new FetchLocationsWithGoogle());
    this._store.dispatch(new FetchProjectIdWithName());
  }
  async ngOnInit() {
    this.globalSettingsData = await firstValueFrom(
      this._store.select(getGlobalSettingsAnonymous).pipe(skipWhile((data) => !Object.keys(data).length))
    );
    this.isProjectSubscription = this.shareDataService.isProjectUnit$.subscribe(
      () => {
        this.modalRef = this.modalService.show(this.noUnitFound, {
          class: 'modal-500 top-modal ip-modal-unset',
          ignoreBackdropClick: true,
        });
      }
    );
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;

    this._store
      .select(getUnitInfo)
      .pipe(takeUntil(this.stopper))
      .subscribe((unitInfo: any) => {
        this.unitInfo = unitInfo;
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        );
        this.minDate = this.currentDate;
      });

    if (!this.conditionalStatus || !this.conditionalStatus.length) {
      this.isLeadStatusLoading = true;
      this._store.dispatch(new FetchLeadStatusList());
      const masterData: any = await combineLatest([
        this._store.select(getStatusMasterData),
        this._store.select(getStatusIsLoading),
      ])
        .pipe(
          filter(([leadStatus, isLoading]) => !isLoading && !!leadStatus),
          map(([leadStatus]) => leadStatus),
          take(1)
        )
        .toPromise();
      if (masterData) { 
        let status = [...masterData]
        if(this.globalSettingsData?.shouldRenameSiteVisitColumn) {
          status = status?.map((item: any) => {
            if(item.displayName === 'Site Visit Scheduled') {
              return {...item, displayName: 'Referral Scheduled',actionName: 'Schedule Referral'}
            }
            return {...item}
          })
        }
        localStorage.setItem('masterleadstatus', JSON.stringify(status));
      }
      this.isLeadStatusLoading = false;
    }
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Leads.UpdateBookedLead')) {
          this.canUpdateBookedLead = true;
        }
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isDualOwnershipEnabled = data?.isDualOwnershipEnabled;
        this.isPastDateSelectionEnabled = data?.isPastDateSelectionEnabled
        this.currencyList =
          data.countries && data.countries.length > 0
            ? data.countries[0].currencies
            : null;
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
        this.isNotesMandatory =
          data?.leadNotesSetting?.isNotesMandatoryOnUpdateLead;
        if (this.isNotesMandatory) {
          toggleValidation(VALIDATION_SET, this.updateForm, 'notes', [
            Validators.required,
          ]);
          
        } else {
          toggleValidation(VALIDATION_CLEAR, this.updateForm, 'notes');
        }
        this.isProjectMandatory =
          data?.leadProjectSetting?.isProjectMandatoryOnBooking;
        this.currency = data?.countries?.length
          ? data.countries[0].currencies
          : null;
      });

    if (
      !this.isBulkUpdate &&
      this.leadInfo?.assignTo !== EMPTY_GUID &&
      this.leadInfo?.assignTo &&
      this.currentPath !== '/invoice'
    )
      this._store.dispatch(
        new FetchReportingManagerDetails(this.leadInfo?.assignTo)
      );

    if (this.leadInfo?.status?.status === 'booked') {
      this.showReasons('Book');
      setTimeout(() => this.makeRequired('Book'));
      this.patchMiniBookDataIfBooked('');
    }

    if (this.leadInfo?.status?.status === 'invoiced') {
      this.showReasons('Invoiced');
      this.patchMiniBookDataIfBooked('');
    }
    this.getMiniBookingFormDatas = this.shareDataService
      .getMiniBookingformData()
      .subscribe((data: any) => {
        if (this.currentPath === '/invoice') {
          this.showReasons('Invoiced');
          this.patchMiniBookDataIfBooked(data?.id);
        }
      });

    // this.initialize();
    this.updateForm.get('currency').valueChanges.subscribe((value) => {
      this.selectedProject.selectedCurrency = value;
    });

    this.updateForm.get('assignedToUserId').valueChanges.subscribe((val) => {
      if (val) {
        this.selectedProject.assignedToUserId = val;
        this._store.dispatch(new FetchReportingManagerDetails(val));
        this._store
          .select(getManagerDetails)
          .pipe()
          .subscribe((data: any) => {
            this.userDetails = data;
          });
      }
    });
    this.updateForm.get('secondaryAssignTo').valueChanges.subscribe((val) => {
      if (val) {
        this.selectedProject.secondaryAssignTo = val;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes?.leadInfo) {
      this.initialize();
    }
  }

  clearManualLocation(location: any) {
    this.manualLocationsList = this.manualLocationsList.filter(
      (manualLocation: any) =>
        JSON.stringify(manualLocation) != JSON.stringify(location)
    );
  }

  addMoreLocation() {
    const { locality, city, state }: any = this.updateForm.value;
    if (!city?.trim() && !state?.trim() && !locality?.trim()) {
      return;
    }
    const filteredLocation = this.manualLocationsList.filter(
      (location: any) => {
        return (
          city?.trim() == location?.city &&
          locality?.trim() == location?.locality &&
          state?.trim() == location?.state
        );
      }
    );

    if (filteredLocation?.length) return;
    this.manualLocationsList.push({
      city: city?.trim(),
      locality: locality?.trim(),
      state: state?.trim(),
    });

    this.removeLocation('changeLocation');
  }

  removeLocation(type: any) {
    switch (type) {
      case 'changeLocation':
        this.updateForm.patchValue({
          locality: null,
          city: null,
          state: null,
        });
        break;
    }
  }

  initialize(): void {
    this.isBulkUpdate = Array.isArray(this.leadInfo);
    this.updateForm = this.formBuilder.group({
      scheduledDate: null,
      notes: null,
      leadStatus: '',
      reason: '',
      leadExpectedBudget: [''],
      revertDate: null,
      purchasedFromWhom: '',
      // updatedLocation: null,
      locationId: [null],
      locality: null,
      city: null,
      state: null,
      currency: [this.leadInfo?.enquiry?.currency || this.defaultCurrency],
      bookedUnderName: [''],
      bookedDate: [this.currentDate],
      agreementValue: [null],
      chosenProperty: null,
      chosenProject: null,
      chosenUnit: [null],
      projectProperty: ['Property'],
      projectsList: [
        this.leadInfo?.projects?.map((project: any) => project?.name),
      ],
      assignedToUserId:
        this.leadInfo?.assignTo != EMPTY_GUID ? this.leadInfo?.assignTo : null,
      secondaryAssignTo:
        this.leadInfo?.secondaryUserId != EMPTY_GUID
          ? this.leadInfo?.secondaryUserId
          : null,
    });
    this.allLeadStatus = JSON.parse(localStorage.getItem('masterleadstatus'));
    this.updateLeadStates =
      this.allLeadStatus?.filter((status: any) => {
        return ![
          'Meeting Done',
          'Site Visit Done',
          'New',
          'Pending',
          'Site Visit Not Done',
          'Meeting Not Done',
        ].includes(status.displayName);
      }) || [];

    this.updateForm.get('leadExpectedBudget').valueChanges.subscribe((val) => {
      this.budgetInWords = formatBudget(
        val,
        this.leadInfo?.enquiry?.currency || this.defaultCurrency
      );
    });

    this._store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => searchStr.length > 2)
      )
      .subscribe((searchStr: string) => {
        this._store.dispatch(new FetchLocationsWithGoogle(searchStr));
      });

    this._store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          ?.slice()
          ?.sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this._store
      .select(getProjectsIDWithNameIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.projectListIsLoading = data;
      });

    this._store
      .select(getPropertyWithIdNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = data.slice().sort((a: any, b: any) => {
          const nameA = a.name || '';
          const nameB = b.name || '';
          return nameA.localeCompare(nameB);
        });
      });

    this._store
      .select(getPropertyWithIdLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.propertyListIsLoading = data;
      });

    const adminsWithReportees$ = this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper));

    const adminsWithReporteesIsLoading$ = this._store
      .select(getAdminsAndReporteesIsLoading)
      .pipe(takeUntil(this.stopper));

    const allUsers$ = this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper));

    const allUsersIsLoading$ = this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper));

    const permissions$ = this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper));

    const permissionsIsLoading$ = this._store
      .select(getPermissionsIsLoading)
      .pipe(takeUntil(this.stopper));

    combineLatest({
      adminsWithReportees: adminsWithReportees$,
      adminsWithReporteesIsLoading: adminsWithReporteesIsLoading$,
      allUsers: allUsers$,
      allUsersIsLoading: allUsersIsLoading$,
      permissions: permissions$,
      permissionsIsLoading: permissionsIsLoading$,
    }).subscribe(
      ({
        adminsWithReportees,
        adminsWithReporteesIsLoading,
        allUsers,
        allUsersIsLoading,
        permissions,
        permissionsIsLoading,
      }) => {
        this.userList = allUsers;
        let userList;
        if (permissions?.includes('Permissions.Leads.Assign')) {
          this.canAssignLead = true;
        }
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          userList = allUsers;
        } else {
          userList = adminsWithReportees;
        }

        let activeUsers = userList?.filter((user: any) => user.isActive);
        this.deactiveUsers = userList?.filter((user: any) => !user.isActive);
        this.primaryUserList = assignToSort(
          activeUsers,
          this.leadInfo?.assignTo
        );
        this.primaryAgentList = assignToSort(
          activeUsers,
          this.leadInfo?.assignTo
        );
        this.secondaryUserList = assignToSort(
          activeUsers,
          this.leadInfo?.secondaryUserId
        );
        this.secondaryUserList = assignToSort(
          activeUsers,
          this.leadInfo?.secondaryUserId
        );
        this.primaryAgentList = this.primaryUserList?.filter(
          (el: any) => !this.leadInfo?.secondaryUserId?.includes(el?.id)
        );
        this.secondaryAgentList = this.secondaryUserList?.filter(
          (el: any) => !this.leadInfo?.assignTo?.includes(el?.id)
        );
        this.updateForm
          .get('assignedToUserId')
          .valueChanges.subscribe((val: any) => {
            this.secondaryAgentList = this.secondaryUserList.filter(
              (el: any) => !val?.includes(el?.id)
            );
          });

        this.updateForm
          .get('secondaryAssignTo')
          .valueChanges.subscribe((val: any) => {
            this.primaryAgentList = this.primaryUserList.filter(
              (el: any) => !val?.includes(el?.id)
            );
          });

        this.isUserListLoading =
          adminsWithReporteesIsLoading &&
          allUsersIsLoading &&
          permissionsIsLoading;
      }
    );

    if (!this.isBulkUpdate) {
      this.leadSource = LeadSource[this.leadInfo?.enquiry?.leadSource];
    }
    this.conditionalStatusRendering();

    this.updateForm.get('currency').valueChanges.subscribe((val) => {
      this.miniagreementValueInWords = formatBudget(
        this.updateForm.value.agreementValue ||
        this.updateForm?.get('agreementValue').value,
        val
      );
    });
    this.updateForm.get('agreementValue').valueChanges.subscribe((val) => {
      this.miniagreementValueInWords = formatBudget(
        val,
        this.updateForm.value.currency
      );
    });
  }

  ngAfterViewInit(): void {
    const element = document.getElementById('bulk-status-table');
    if (element) {
      this.elementHeight = 'unset';
      const calculatedHeight = `${this.elementHeight}`;
      this.statusForm.nativeElement.style.height = calculatedHeight;
    }
    this.cdr.detectChanges();
  }

  dateValidator(control: { value: any }) {
    const selectedDate = control.value;

    if (selectedDate) {
      const currentDate = new Date();
      const selectedDateTime = new Date(selectedDate);
      currentDate.setSeconds(0, 0);
      selectedDateTime.setSeconds(0, 0);
      if (selectedDateTime > currentDate) {
        return { invalidDate: true };
      }
    }
    return null;
  }

  conditionalStatusRendering() {
    this.conditionalStatus = this.updateLeadStates;
    // this.conditionalStatus = this.conditionalStatus.filter((item:any) => item.actionName !== 'Invoiced');
    // .filter((state: any) => {
    //   switch (
    //   this.leadInfo?.leadStatus?.baseStatus ||
    //   this.leadInfo?.[0]?.leadStatus?.baseStatus
    //   ) {
    //     case this.currentLeadStatus['meeting-scheduled']:
    //     case this.currentLeadStatus['visit-scheduled']:
    //       return !(state.actionName == UPDATE_STATUS['drop']);
    //     case UPDATE_STATUS['not-interested']:
    //       return !(
    //         state.actionName == UPDATE_STATUS['not-interested'] ||
    //         state.actionName == UPDATE_STATUS['book']
    //       );
    //     case this.currentLeadStatus['dropped']:
    //       return false;
    //     case this.currentLeadStatus['booked']:
    //       return false;
    //     case UPDATE_STATUS['callback']:
    //       return !(
    //         state.actionName == UPDATE_STATUS['drop'] ||
    //         state.actionName == UPDATE_STATUS['book'] ||
    //         state.actionName == UPDATE_STATUS['callback']
    //       );
    //     default:
    //       return state.actionName;
    //   }
    // });
    return this.conditionalStatus;
  }

  openAppointmentPopup() {
    let initialState: any = {
      data: this.leadInfo,
      closeModal: () => {
        appointmentModalRef.hide();
      },
      closeLeadPreviewModal: this.closeLeadPreviewModal,
    };
    const appointmentModalRef = this.modalService.show(
      LeadAppointmentComponent,
      {
        initialState,
        class: 'right-modal modal-550 ip-modal-unset',
      }
    );
  }

  showReasons(status: any) {
    this.selectedStatus = status;
    if (
      (this.leadInfo?.status?.status === 'invoiced' ||
        this.leadInfo?.status?.status === 'booked') &&
      status === 'Book'
    ) {
      this.patchMiniBookDataIfBooked('');
    }

    if (this.currentPath === '/invoice') {
      if (status === 'Invoiced') {
        this.isShowBookingFormBtn = true;
      } else if (status === 'Booking Cancel') {
        this.isShowBookingFormBtn = false;
      }
    } else {
      if (status === 'Book' || status === 'Booked') {
        this.isShowBookingFormBtn = true;
      } else {
        this.isShowBookingFormBtn = false;
      }
    }

    patchFormControlValue(this.updateForm, 'leadStatus', status);
    this.updateLeadStates.map((item: any) => {
      if (status == item.actionName) {
        if (item?.childTypes?.length) {
          this.callBackReason = item.childTypes;
          this.hideStatus = true;
        }
        this.makeRequired(item.actionName);
        this.selectedStatus = item.actionName;
      }
    });
    let childCount = this.updateLeadStates.filter(
      (statusObj: any) => statusObj?.actionName === status
    )?.[0]?.childTypes?.length;
    if (childCount) {
      toggleValidation(VALIDATION_SET, this.updateForm, 'reason', [
        Validators.required,
      ]);
    } else {
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'reason');
    }
  }

  setSelectedReason(reason: any) {
    this.selectedReason = reason;
  }

  makeRequired(leadStatusValue: any) {
    if (
      [
        UPDATE_STATUS['callback'],
        UPDATE_STATUS['schedule-meeting'],
        UPDATE_STATUS['schedule-site-visit'],
      ].includes(leadStatusValue)
    ) {
      toggleValidation(VALIDATION_SET, this.updateForm, 'scheduledDate', [
        Validators.required,
      ]);
    } else {
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'scheduledDate');
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'reason');
    }

    if ([UPDATE_STATUS['book']].includes(leadStatusValue)) {
      // toggleValidation(VALIDATION_SET, this.updateForm, 'agreementValue', [
      //   Validators.required,
      // ]);
      toggleValidation(VALIDATION_SET, this.updateForm, 'bookedUnderName', [
        Validators.required,
      ]);
      toggleValidation(VALIDATION_SET, this.updateForm, 'projectProperty', [
        Validators.required,
      ]);
      toggleValidation(VALIDATION_SET, this.updateForm, 'bookedDate', [
        Validators.required,
      ]);
      if (this.updateForm.value.projectProperty === 'Property') {
        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProperty', [
          Validators.required,
        ]);
      } else {
        if (this.isProjectMandatory) {
          toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProject', [
            Validators.required,
          ]);
        }
      }
    } else {
      // toggleValidation(VALIDATION_CLEAR, this.updateForm, 'agreementValue');
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'bookedUnderName');
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'projectProperty');
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'bookedDate');
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');
    }
  }

  deselectStatuses() {
    this.updateForm.controls['reason'].setValue('');
    this.updateForm.controls['leadStatus'].setValue('');
    this.selectedStatus = '';
    this.hideStatus = false;
    this.selectedReason = '';
    this.isShowBookingFormBtn = false;
  }

  updateStatus(isSaveAndNext: boolean = false) {
    Object.keys(this.updateForm.controls).forEach((field) => {
      const control = this.updateForm.get(field);
      if (control && control.invalid) {
        console.log(`Invalid field: ${field}`);
      }
    });
    if (!this.isBulkUpdate && !this.leadInfo?.name?.trim()) {
      this._notificationsService?.warn(
        'Lead Name is invalid, Please Rename to continue'
      );
      return;
    } else if (this.isBulkUpdate) {
      for (const lead of this.leadInfo) {
        if (!lead?.name?.trim()) {
          this._notificationsService?.warn(
            'Lead Name is invalid, Please Rename to continue'
          );
          return;
        }
      }
    }
    if (this.updateForm.invalid) {
      validateAllFormFields(this.updateForm);
      return;
    }
    this.selectedProject.agreementValue =
      this.updateForm?.value?.agreementValue;
    const userData = this.updateForm.value;
    let postPonedStatusId = '';
    this.allLeadStatus?.map((status: any) => {
      status?.childTypes?.map((child: any) => {
        if (
          child?.displayName ===
          LEAD_STATUS_REASONS[UPDATE_STATUS['callback']]['plan-postponed']
        ) {
          postPonedStatusId = child?.id;
        }
      });
    });

    this.addMoreLocation();

    let addressPayload: any = (
      (this.showLocationSearch
        ? userData?.locationId
        : this.manualLocationsList) || this.leadInfo?.locationId
    )?.map((location: any) => {
      return {
        locationId: location?.id ?? location?.id,
        placeId: location?.placeId ?? location?.placeId,
        subLocality:
          (location?.enquiredLocality ?? location?.enquiredLocality) ||
          (location?.locality ?? location?.locality),
        city:
          (location?.enquiredCity ?? location?.enquiredCity) ||
          (location?.city ?? location?.city),
        state:
          (location?.enquiredState ?? location?.enquiredState) ||
          (location?.state ?? location?.state),
      };
    });

    // {
    //   locationId:
    //     userData.locationId?.id ||
    //     this.leadInfo.locationId?.id ||
    //     undefined,
    //   placeId:
    //     userData.locationId?.placeId ||
    //     this.leadInfo.locationId?.placeId ||
    //     undefined,
    //   subLocality: this.leadInfo.locality || userData.locality || undefined,
    //   city: this.leadInfo.city || userData.city || undefined,
    //   state: this.leadInfo.state || userData.state || undefined,
    // };

    let isAddressPayloadEmpty = !addressPayload?.length;

    // for (const prop in addressPayload) {
    //   if (addressPayload[prop] !== undefined) {
    //     isAddressPayloadEmpty = false;
    //     break;
    //   }
    // }

    let payLoad: any = {
      id: this.leadInfo.id,
      leadStatusId:
        userData.reason || userData.leadStatus || this.leadInfo.leadStatusId,
      scheduledDate:
        setTimeZoneDateWithTime(
          userData.scheduledDate,
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        ) || undefined,
      notes: userData.notes ? userData.notes : this.leadInfo.notes,
      IsNotesUpdated: userData.notes ? true : false,
      bookedUnderName: userData.bookedUnderName,
      unmatchedBudget:
        userData.leadExpectedBudget ||
        this.leadInfo.unmatchedBudget ||
        undefined,
      purchasedFrom: userData.purchasedFromWhom || this.leadInfo.purchasedFrom,
      addresses: isAddressPayloadEmpty ? null : addressPayload,
      postponedDate:
        userData.reason === postPonedStatusId
          ? setTimeZoneDateWithTime(
            userData.scheduledDate,
            this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
          )
          : setTimeZoneDateWithTime(
            userData?.revertDate,
            this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
          ) || undefined,

      projectsList:
        userData.leadStatus === 'Book'
          ? [
            ...new Set([
              ...(Array.isArray(userData.chosenProject)
                ? userData.chosenProject.map(
                  (projectId: any) =>
                    this.projectList?.find(
                      (project: any) => project.id === projectId
                    )?.name
                )
                : userData.chosenProject
                  ? [
                    this.projectList?.find(
                      (project: any) => project.id === userData.chosenProject
                    )?.name,
                  ]
                  : []),
              ...(this.leadInfo?.projects?.map(
                (project: any) => project?.name
              ) || []),
            ]),
          ]
          : userData.projectsList?.length
            ? userData.projectsList
            : this.leadInfo?.projects?.map((project: any) => project?.name),

      propertiesList:
        userData.leadStatus === 'Book'
          ? [
            ...new Set([
              ...(Array.isArray(userData.chosenProperty)
                ? userData.chosenProperty.map(
                  (propertyId: any) =>
                    this.propertyList?.find(
                      (property: any) => property.id === propertyId
                    )?.title
                )
                : userData.chosenProperty
                  ? [
                    this.propertyList?.find(
                      (property: any) =>
                        property.id === userData.chosenProperty
                    )?.title,
                  ]
                  : []),
              ...(this.leadInfo?.properties?.map(
                (property: any) => property?.title
              ) || []),
            ]),
          ]
          : userData.propertiesList?.length
            ? userData.propertiesList
            : this.leadInfo?.properties?.map((property: any) => property?.title),
      bookedDate:
        setTimeZoneDateWithTime(
          userData.bookedDate,
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        ) || undefined,
      agreementValue: userData.agreementValue ?? null,
      propertyIds: userData.chosenProperty ? [userData.chosenProperty] : null,
      projectIds: userData?.chosenProject ? [userData?.chosenProject] : null,
      unitTypeId: userData?.chosenUnit ?? null,
      currency: userData.currency,
    };
    if (userData.assignedToUserId) {
      payLoad.assignTo = userData?.assignedToUserId || this.leadInfo?.assignTo;
    } else {
      payLoad.assignTo = EMPTY_GUID;
    }

    if (userData.secondaryAssignTo) {
      payLoad.secondaryUserId =
        userData?.secondaryAssignTo || this.leadInfo.secondaryUserId;
    } else {
      payLoad.secondaryUserId = EMPTY_GUID;
    }

    if (
      this.leadInfo?.status?.status == 'meeting_scheduled' ||
      this.leadInfo?.status?.status == 'site_visit_scheduled'
    ) {
      payLoad.isFullyCompleted = true;
      payLoad.projects = this.leadInfo?.projects?.map((project: any) =>
        typeof project === 'string' ? project : project?.name
      );
      payLoad.properties = this.leadInfo?.properties?.map(
        (property: any) => property?.title
      );
    }
    this.selectedProject.secondaryUserId = payLoad.secondaryUserId;
    if (!this.isBulkUpdate) {
      this.leadStatusIsLoading = true;
      this._store.dispatch(
        new UpdateLeadStatus(
          payLoad,
          this.leadInfo.id,
          false,
          this.selectedStatus === 'Book'
        )
      );
      if (!this.canShowStatusPopupInPreview)
        this._store.dispatch(new LeadPreviewChanged());
    } else {
      this.multipleLeadsIsLoading = true;
      if (!userData.leadStatus) {
        this.modalService.hide();
        return;
      }
      const notBookedLeads =
        this.currentPath !== '/invoice'
          ? this.leadInfo.filter(
            (lead: any) =>
              lead.status.displayName !== 'Booked' &&
              lead.status.displayName !== 'Invoiced'
          )
          : this.leadInfo;

      const leadItem = this.isSelectedOnlySomeBooked()
        ? notBookedLeads
        : this.leadInfo;

      const idArray: string[] = leadItem.map((lead: any) => lead.id);
      let payloadObj: any = {
        LeadIds: idArray,
        leadStatusId: userData?.reason || userData?.leadStatus,
        notes: userData?.notes,
        IsNotesUpdated: userData.notes ? true : false,
        scheduledDate:
          setTimeZoneDateWithTime(
            userData?.scheduledDate,
            this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
          ) || null,
        bookedDate:
          setTimeZoneDateWithTime(
            userData?.bookedDate,
            this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
          ) || null,
        revertDate:
          setTimeZoneDateWithTime(
            userData?.revertDate,
            this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
          ) || null,
        unmatchedBudget: userData?.leadExpectedBudget || null,
        purchasedFrom: userData?.purchasedFromWhom,
        bookedUnderName: userData?.bookedUnderName,
        addresses: isAddressPayloadEmpty ? null : addressPayload,
        agreementValue: userData.agreementValue ?? null,
        postponedDate:
          userData.reason === postPonedStatusId
            ? setTimeZoneDateWithTime(
              userData?.scheduledDate,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
            )
            : setTimeZoneDateWithTime(
              userData?.revertDate,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
            ) || null,
        projectsList:
          userData.leadStatus === 'Book'
            ? [
              ...new Set([
                ...(Array.isArray(userData.chosenProject)
                  ? userData.chosenProject.map(
                    (projectId: any) =>
                      this.projectList?.find(
                        (project: any) => project.id === projectId
                      )?.name
                  )
                  : userData.chosenProject
                    ? [
                      this.projectList?.find(
                        (project: any) =>
                          project.id === userData.chosenProject
                      )?.name,
                    ]
                    : []),
              ]),
            ]
            : userData?.projectsList?.length
              ? userData?.projectsList
              : [],
        propertiesList:
          userData.leadStatus === 'Book'
            ? [
              ...new Set([
                ...(Array.isArray(userData.chosenProperty)
                  ? userData.chosenProperty.map(
                    (propertyId: any) =>
                      this.propertyList?.find(
                        (property: any) => property.id === propertyId
                      )?.title
                  )
                  : userData.chosenProperty
                    ? [
                      this.propertyList?.find(
                        (property: any) =>
                          property.id === userData.chosenProperty
                      )?.title,
                    ]
                    : []),
              ]),
            ]
            : userData.propertiesList?.length
              ? userData.propertiesList
              : [],
        propertyIds: userData?.chosenProperty
          ? [userData.chosenProperty]
          : null,
        projectIds: userData?.chosenProject ? [userData.chosenProject] : null,
        isFullyCompleted: leadItem.some((lead: any) =>
          ['meeting_scheduled', 'site_visit_scheduled'].includes(
            lead?.status?.status
          )
        ),
        assignTo: userData.assignedToUserId || null,
        secondaryUserId: userData.secondaryAssignTo || null,
        unitTypeId: userData?.chosenUnit ?? null,
      };

      this._store.dispatch(new UpdateMultipleLead(payloadObj, true));

      this._store
        .select(getMultipleLeadStatusIsLoading)
        .pipe(
          skipWhile((isLoading) => isLoading),
          take(1)
        )
        .subscribe((isLoading: boolean) => {
          this.multipleLeadsIsLoading = isLoading;
          this.modalService.hide();
          const intervalId = setInterval(() => {
            if (this.modalService.getModalsCount() === 0) {
              const numberOfLeads = payloadObj.LeadIds?.length;

              if (numberOfLeads >= 50) {
                this.modalRef = this.modalService.show(
                  this.trackerInfoModal,
                  Object.assign(
                    {},
                    {
                      class: 'modal-400 top-modal ph-modal-unset',
                      ignoreBackdropClick: true,
                      keyboard: false,
                    }
                  )
                );
              }
            }
            clearInterval(intervalId);
          }, 2000);
        });
    }
    this._store
      .select(getLeadStatusIsLoading)
      .pipe(
        skipWhile((isLoading) => isLoading),
        take(1)
      )
      .subscribe((isLoading: boolean) => {
        let masterLeadStatus = JSON.parse(
          localStorage.getItem('masterleadstatus')
        );
        const id =
          userData.reason || userData.leadStatus || this.leadInfo.leadStatusId;
        const status = masterLeadStatus.find(
          (item: any) =>
            item.id === id ||
            item.childTypes.some((child: any) => child.id === id)
        );
        this.leadStatusIsLoading = isLoading;
        if (this.leadInfo?.status?.displayName)
          this.leadInfo = {
            ...this.leadInfo,
            status: {
              ...this.leadInfo.status,
              displayName: status.displayName,
            },
          };
        if (this.leadInfo?.projects)
          this.leadInfo.projects = userData?.projectsList;
        if (this.leadInfo?.properties)
          if (this.leadInfo?.notes) this.leadInfo.notes = userData?.notes;
        if (this.leadInfo?.scheduledDate)
          this.leadInfo.scheduledDate = userData?.scheduledDate;
        if (this.leadInfo?.assignTo)
          this.leadInfo.assignTo =
            userData?.assignedToUserId != ''
              ? userData?.assignedToUserId || this.leadInfo?.assignTo
              : EMPTY_GUID;
        if (this.leadInfo?.secondaryUserId)
          this.leadInfo.secondaryUserId =
            userData?.secondaryAssignTo != ''
              ? userData?.secondaryAssignTo || this.leadInfo.secondaryUserId
              : EMPTY_GUID;
        let hasSubStatus = false;
        for (let reason of this.callBackReason) {
          if (
            this.updateForm?.controls?.['reason']?.value === reason?.id &&
            this.leadInfo?.status?.childType
          ) {
            const updatedChildType = {
              ...this.leadInfo.status.childType,
              displayName: reason?.displayName,
            };
            this.leadInfo.status = {
              ...this.leadInfo.status,
              childType: updatedChildType,
            };
            hasSubStatus = true;
            break;
          }
        }
        if (!hasSubStatus) {
          this.leadInfo.status.childType = {
            ...this.leadInfo?.status?.childType,
            displayName: '',
          };
        }
        if (!this.isBulkUpdate) this.cleanStatusForm();
        if (!isSaveAndNext) {
          this.modalRef.hide();
          return;
        }
        this._leadPreviewComponent.nextData();
      });
    if (isSaveAndNext) {
      this.trackingService.trackFeature(
        `Web.Leads.Button.SaveandNext.Click`,
        this.leadInfo.id
      );
    } else {
      this.trackingService.trackFeature(
        `Web.Leads.Button.SaveandClose.Click`,
        this.leadInfo.id
      );
    }
  }

  openBulkUpdatedStatus() {
    this._store.dispatch(new FetchBulkOperation(1, 10, 'lead'));
    this.modalRef = this.modalService.show(BulkOperationTrackerComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState: {
        moduleType: 'lead',
      },
    });
  }

  cleanStatusForm(): void {
    this.deselectStatuses();
    this.updateForm.controls['scheduledDate'].setValue('');
    this.updateForm.controls['notes'].setValue('');
    this.updateForm.controls['projectsList'].setValue([]);
  }

  fullBookingFormModal() {
    if (this.updateForm.invalid) {
      validateAllFormFields(this.updateForm);
      return;
    }
    if (this.updateForm.value.notes)
      this.selectedProject.notes = this.updateForm.value.notes;
    this.isShowBookingFormBtn = false;
    this.updateStatus(true);
    this._store
      .select(getManagerDetails)
      .pipe(take(1))
      .subscribe((data: any) => {
        this.userDetails = data;
      });
    let initialState: any = {
      selectedProject: this.selectedProject,
      leadInfo: this.leadInfo,
      userDetails: this.userDetails,
      miniBookDate: this.updateForm.get('bookedDate').value,
    };
    this.modalRef = this.modalService.show(BookingFormComponent, {
      class: 'right-modal modal-550 ip-modal-unset',
      initialState,
    });
  }

  onPropertyChange(property: any) {
    this.onPropertySelect$.next();
    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');
    this.isSelectedPropertyOrProject = 'Property';
    this.isShowUnitInfoField = false;
    if (property?.id) {
      this._store.dispatch(new FetchPropertyById(property?.id));
    }
    this._store
      .select(getPropertyListDetails)
      .pipe(takeUntil(this.onPropertySelect$))
      .subscribe((data: any) => {
        if (data) {
          this.selectedProject.selectedpropertyname = data.title;
          this.selectedProject.buildername = data.ownerDetails?.name;
          this.selectedProject.sealablearea = data.dimension?.saleableArea;
          this.selectedProject.carpetarea = data.dimension?.carpetArea;
          this.selectedProject.brokerageCurrency =
            data.monetaryInfo?.brokerageCurrency;
          this.selectedProject.brockragecharge = data.monetaryInfo?.brokerage;
          this.selectedProject.isprojectproperty =
            this.isSelectedPropertyOrProject;
        }
      });
  }

  onProjectChange(project: any) {
    this.onProjectSelect$.next();
    if (project?.id) {
      this._store.dispatch(new FetchProjectById(project?.id));
      if (this.isProjectMandatory) {
        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenUnit', [
          Validators.required,
        ]);
        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProject', [
          Validators.required,
        ]);
      }
    } else {
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');
      if (this.isProjectMandatory) {
        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProject', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');
      }
    }

    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');
    this.isSelectedPropertyOrProject = 'Project';
    this.isShowUnitInfoField = true;
    this.updateForm.get('chosenUnit').reset();
    this._store
      .select(getIsProjectByIdLoading)
      .pipe(takeUntil(this.onProjectSelect$))
      .subscribe((data: any) => {
        this.isUnitInfoDataLoading = data;
      });

    this._store
      .select(getSelectedProjectById)
      .pipe(takeUntil(this.onProjectSelect$))
      .subscribe((data: any) => {
        if (data) {
          this.unitInfo = data.unitTypes;
          this.selectedProject.selectedpropertyname = data.name;
          this.selectedProject.buildername = data.builderDetail?.name;
          this.selectedProject.brockragecharge = data.monetaryInfo?.brokerage;
          this.selectedProject.brokerageCurrency =
            data.monetaryInfo?.brokerageCurrency;
          this.selectedProject.isprojectproperty =
            this.isSelectedPropertyOrProject;
          this.selectedProject.allprojectdata = data;
        }
      });
  }

  onChosenUnitChange(event: any) {
    this.unitInfo?.map((data: any) => {
      if (data.id === event?.id) {
        this.selectedProject.carpetarea = data?.carpetArea;
        this.selectedProject.sealablearea = data?.superBuildUpArea;
        this.selectedProject.unitname = data?.name;
      }
    });
  }
  onInputAgreementValue(value: number) {
    this.selectedProject.agreementValue = value;
  }

  switchTabProjectProperty(value: any) {
    if (value === 'Property') {
      this.isShowUnitInfoField = false;
      this.updateForm.controls['projectsList'].setValue(null);
      this.updateForm.controls['chosenProject'].setValue(null);
      toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProperty', [
        Validators.required,
      ]);
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');
    } else {
      this.isShowUnitInfoField = true;
      this.updateForm.controls['chosenProperty'].setValue(null);
      if (this.isProjectMandatory) {
        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProject', [
          Validators.required,
        ]);
      }
      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');
    }
  }

  patchMiniBookDataIfBooked(id: string) {
    if (id !== '') {
      this._store.dispatch(new FetchInvoiceById(id));
    } else {
      this._store.dispatch(new FetchInvoiceById(this.leadInfo.id));
    }
    this._store.select(getBookingData).subscribe((bookingDetails: any) => {
      if (bookingDetails) {
        if (
          Array.isArray(bookingDetails.projects) &&
          bookingDetails.projects.length > 0
        ) {
          this.onProjectChange(bookingDetails.projects[0].id);
        }
        if (
          Array.isArray(bookingDetails.properties) &&
          bookingDetails.properties.length > 0
        ) {
          this.onPropertyChange(bookingDetails.properties[0].id);
        }
        this.updateForm
          .get('reason')
          .setValue(this.leadInfo?.status?.childType?.id);
        this.updateForm.get('leadStatus').setValue(this.leadInfo?.status?.id);
        this.selectedReason = this.leadInfo?.status?.childType?.displayName;
        const projectPropertyControl = this.updateForm.get('projectProperty');
        if (
          Array.isArray(bookingDetails.projects) &&
          bookingDetails.projects.length > 0
        ) {
          projectPropertyControl.setValue('Project');
        } else if (
          Array.isArray(bookingDetails.properties) &&
          bookingDetails.properties.length > 0
        ) {
          projectPropertyControl.setValue('Property');
        }
        this.updateForm.patchValue({
          bookedUnderName: bookingDetails.bookedUnderName,
          bookedDate: patchTimeZoneWithTime(
            bookingDetails.bookedDate,
            this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
          ),
          agreementValue:
            bookingDetails.agreementValue > 0
              ? bookingDetails.agreementValue
              : null,
          chosenProperty:
            Array.isArray(bookingDetails.properties) &&
              bookingDetails.properties.length > 0
              ? bookingDetails.properties[0]?.id
              : null,
          chosenProject:
            Array.isArray(bookingDetails.projects) &&
              bookingDetails.projects.length > 0
              ? bookingDetails.projects[0]?.id
              : null,
          chosenUnit: bookingDetails?.unitType?.id,
          notes: bookingDetails.notes,
          currency: bookingDetails.currency,
        });

        if (bookingDetails.unitType?.id) {
          this.onChosenUnitChange(bookingDetails.unitType.id);
        }
      }
    });
  }

  closeModal() {
    this.isVisitDone = false;
    this.modalRef.hide();
    this.updateForm.reset();
    this.hideStatus = false;
    this.selectedReason = '';
  }

  checkBookedUnderName(event: any) {
    const inputValue = event.target.value.trim();
    if (inputValue) {
      this.updateForm.controls['bookedUnderName'].setValue(inputValue);
      this.updateForm.controls['bookedUnderName'].setErrors(null);
    } else {
      this.updateForm.controls['bookedUnderName'].setErrors({
        whitespace: true,
      });
    }
  }

  goToBack() {
    this.shareDataService.gotoOverviewTab('Overview');
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
    this.isProjectSubscription?.unsubscribe();
    this.onProjectSelect$.next();
    this.onProjectSelect$.complete();
    this.onPropertySelect$.next();
    this.onPropertySelect$.complete();
  }
}
