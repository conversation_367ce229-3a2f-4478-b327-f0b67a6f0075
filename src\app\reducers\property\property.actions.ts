import { Action } from '@ngrx/store';
import {
  LeadExcel,
  MapColumnsExcel,
} from 'src/app/core/interfaces/leads.interface';
import {
  BulkAssignProperty,
  PropertiesFilter,
  Property,
  PropertyResponse,
} from 'src/app/core/interfaces/property.interface';

export enum PropertyActionTypes {
  FETCH_PROPERTY_LIST = '[PROPERTY] Fetch Property List',
  FETCH_PROPERTY_LIST_SUCCESS = '[PROPERTY] Fetch Property List Success',
  FETCH_PROPERTY_LEADS_COUNT_BY_IDS = '[PROPERTY] Fetch Property Leads Count By Ids',
  FETCH_PROPERTY_LEADS_COUNT_BY_IDS_SUCCESS = '[PROPERTY] Fetch Property Leads Count By Ids Success',
  FETCH_PROPERTY_BY_ID = '[PROPERTY] Fetch Property By Id',
  FETCH_PROPERTY_BY_ID_SUCCESS = '[PROPERTY] Fetch Property By Id Success',
  ADD_PROPERTY = '[PROPERTY] Add Property',
  ADD_PROPERTY_SUCCESS = '[PROPERTY] Add Property Success',
  DELETE_PROPERTY = '[PROPERTY] Delete Property',
  UPDATE_PROPERTY = '[PROPERTY] Update Property',
  UPDATE_PROPERTY_SUCCESS = '[PROPERTY] Update Property Success',
  UPDATE_PROPERTY_STATUS = '[PROPERTY] Update Property Status',
  UPDATE_USER_ASSIGNMENT = '[PROPERTY] Update User Assignment',
  UPDATE_USER_ASSIGNMENT_SUCCESS = '[PROPERTY] Update User Assignment Success',
  BULK_REASSIGN_PROPERTY = '[PROPERTY] Bulk Reassign Property',
  BULK_REASSIGN_PROPERTY_SUCCESS = '[PROPERTY] Bulk Reassign Property Success',
  FETCH_PROPERTY_SUBMITTED_ID = '[PROPERTY] Fetch Property Submitted Id',
  INCREASE_SHARE_COUNT = '[PROPERTY] Increase Share Count',
  UPDATE_FILTER_PAYLOAD = '[PROPERTY] Update Filter Payload',
  FETCH_LOCATION_LIST = '[PROPERTY] Fetch Location List',
  FETCH_LOCATION_LIST_SUCCESS = '[PROPERTY] Fetch Location List Success',
  ARCHIVE_PROPERTY = '[PROPERTY] Archive Property',
  FETCH_BROCHURE_LIST = '[PROPERTY] Fetch Brochure List',
  FETCH_BROCHURE_LIST_SUCCESS = '[PROPERTY] Fetch Brochure List Success',
  UPLOAD_BROCHURE = '[PROPERTY] Update Brochure',
  UPLOAD_BROCHURE_SUCCESS = '[PROPERTY] Update Brochure Success',
  FETCH_GALLERY_DROPDOWN_DATA = '[PROPERTY] Fetch Gallery Dropdown Data',
  FETCH_GALLERY_DROPDOWN_DATA_SUCCESS = '[PROPERTY] Fetch Gallery Dropdown Data Success',
  UPDATE_GALLERY = '[PROPERTY] Update Gallery',
  FETCH_MATCHING_LEADS = '[PROPERTY] Matching Lead',
  FETCH_MATCHING_LEADS_LIST_SUCCESS = '[PROPERTY] Fetch Matching Leads Success',
  FETCH_ARCHIVED_PROPERTY_LIST = '[PROPERTY] Fetch Archived properties list',
  FETCH_ARCHIVED_PROPERTY_LIST_SUCCESS = '[PROPERTY] Fetch Archived properties list Success',
  PROPERTY_EXCEL_UPLOAD = '[PROPERTY] Upload Property Excel File',
  PROPERTY_EXCEL_UPLOAD_SUCCESS = '[PROPERTY] Upload Property Excel File Success',
  UPLOAD_MAPPED_COLUMNS = '[PROPERTY] Upload Mapped Column Data',
  FETCH_EXCEL_UPLOADED_LIST = '[PROPERTY] Fetch Excel Uploaded List',
  FETCH_EXCEL_UPLOADED_LIST_SUCCESS = '[PROPERTY] Fetch Excel Uploaded List Success',
  EXPORT_PROPERTY = '[PROPERTY] export Property through excel',
  EXPORT_PROPERTY_SUCCESS = '[PROPERTY] export Property through excel Success',
  FETCH_EXPORT_PROPERTY_STATUS = '[PROPERTY] Fetch Export Status List',
  FETCH_EXPORT_PROPERTY_STATUS_SUCCESS = '[PROPERTY] Fetch Export Status List Success',
  FETCH_OWNER_NAMES = '[PROPERTY] Fetch Owner Names',
  FETCH_OWNER_NAMES_SUCCESS = '[PROPERTY] Fetch Owner Names Success',
  FETCH_PROPERTY_EXPORT = '[PROPERTY] Fetch Property Export',
  FETCH_PROPERTY_EXPORT_SUCCESS = '[PROPERTY] Fetch Property Export Success',
  FETCH_MICROSITE_PROPERTY = '[PROPERTY] Fetch Microsite Property Details',
  FETCH_MICROSITE_PROPERTY_SUCCESS = '[PROPERTY] Fetch Microsite Property Details Success',
  FETCH_MICROSITE_SIMILAR_PROPERTIES = '[PROPERTY] Fetch Microsite Similar Properties',
  FETCH_MICROSITE_SIMILAR_PROPERTIES_SUCCESS = '[PROPERTY] Fetch Microsite Similar Properties Success',
  ADD_MS_LEAD = '[PROPERTY] Add Microsite Lead',
  ADD_MS_LEAD_SUCCESS = '[PROPERTY] Add Microsite Lead Success',
  FETCH_MICROSITE_USER_DETAILS = '[PROPERTY] Fetch Microsite User Details',
  FETCH_MICROSITE_USER_DETAILS_SUCCESS = '[PROPERTY] Fetch Microsite User Details Success',
  FETCH_PROPERTY_CURRENCY_LIST = '[PROPERTY] Fetch Property Currency List',
  FETCH_PROPERTY_CURRENCY_LIST_SUCCESS = '[PROPERTY] Fetch Property Currency List Success',
  ADD_WATER_MARK = '[PROPERTY] Add Water Mark On Images',
  ADD_WATER_MARK_SUCCESS = '[PROPERTY] Add Water Mark On Images Success',
  FETCH_PROPERTY_WITH_ID_NAME_LIST = '[LEAD] Fetch Property With Id Name List',
  FETCH_PROPERTY_WITH_ID_NAME_LIST_SUCCESS = '[LEAD] Fetch Property With Id Name List Success',
  FETCH_PROPERTY_COUNT = '[PROPERTY] Fetch Property Count',
  FETCH_PROPERTY_COUNT_SUCCESS = '[PROPERTY] Fetch Property Count Success',
  FETCH_PROPERTY_ASSIGNMENTS = '[PROPERTY] Fetch Property Assignments',
  FETCH_PROPERTY_ASSIGNMENTS_SUCCESS = '[PROPERTY] Fetch Property Assignments Success',
  CLONE_PROPERTY = '[PROPERTY] Clone Property',
  CLONE_PROPERTY_SUCCESS = '[PROPERTY] Clone Property Success',
  FETCH_PROPERTY_MODIFIED_ON = '[PROPERTY] Fetch Property Modified On',
  FETCH_PROPERTY_MODIFIED_ON_SUCCESS = '[PROPERTY] Fetch Property Modified On Success',
  ADD_LISTING = '[PROPERTY] Add Listing',
  ADD_LISTING_SUCCESS = '[PROPERTY] ADD_LISTING_SUCCESS',
  UPDATE_LISTING = '[PROPERTY] UPDATE_LISTING',
  UPDATE_LISTING_SUCCESS = '[PROPERTY] UPDATE_LISTING_SUCCESS',
  FETCH_LISTING_BY_ID = '[PROPERTY] Fetch Listing By Id',
  FETCH_LISTING_BY_ID_SUCCESS = '[PROPERTY] Fetch Listing By Id Success',
  FETCH_PF_LOCATION = '[PROPERTY] Fetch PF Location',
  FETCH_PF_LOCATION_SUCCESS = '[PROPERTY] Fetch PF Location Success',
  FETCH_PROPERTY_WITH_GOOGLE_LOCATION = '[PROPERTY] Fetch Property With Google Location',
  FETCH_PROPERTY_WITH_GOOGLE_LOCATION_SUCCESS = '[PROPERTY] Fetch Property With Google Location Success',
}
export class FetchPropertyList implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_LIST;
  constructor(public canFetchLeadsCount: boolean = false) { }
}
export class FetchPropertyListSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_LIST_SUCCESS;
  constructor(
    public properties: PropertyResponse<Property> = {} as PropertyResponse<Property>
  ) { }
}
export class FetchPropertyLeadsCountByIds implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_LEADS_COUNT_BY_IDS;
  constructor(public ids: string[]) { }
}
export class FetchPropertyLeadsCountByIdsSuccess implements Action {
  readonly type: string =
    PropertyActionTypes.FETCH_PROPERTY_LEADS_COUNT_BY_IDS_SUCCESS;
  constructor(public counts: any) { }
}
export class UpdateFilterPayload implements Action {
  readonly type: string = PropertyActionTypes.UPDATE_FILTER_PAYLOAD;
  constructor(public filter: PropertiesFilter) { }
}
export class FetchPropertyById implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_BY_ID;
  constructor(public id: string) { }
}
export class FetchPropertyByIdSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_BY_ID_SUCCESS;
  constructor(public selectedProperty: any) { }
}
export class FetchActivePropertyId implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_SUBMITTED_ID;
  constructor(public propertyId: any) { }
}
export class AddProperty implements Action {
  readonly type: string = PropertyActionTypes.ADD_PROPERTY;
  constructor(public payload: any) { }
}

export class AddPropertySuccess implements Action {
  readonly type: string = PropertyActionTypes.ADD_PROPERTY_SUCCESS;
  constructor(public resp: any) { }
}

export class UpdateProperty implements Action {
  readonly type: string = PropertyActionTypes.UPDATE_PROPERTY;
  constructor(public payload: any, public id: string) { }
}

export class UpdatePropertySuccess implements Action {
  readonly type: string = PropertyActionTypes.UPDATE_PROPERTY_SUCCESS;
  constructor(public resp: any) { }
}

export class UpdatePropertyStatus implements Action {
  readonly type: string = PropertyActionTypes.UPDATE_PROPERTY_STATUS;
  constructor(public id: string) { }
}
export class DeleteProperty implements Action {
  readonly type: string = PropertyActionTypes.DELETE_PROPERTY;
  constructor(public id: string) { }
}
export class UpdateUserAssignment implements Action {
  readonly type: string = PropertyActionTypes.UPDATE_USER_ASSIGNMENT;
  constructor(public payload: BulkAssignProperty) { }
}
export class UpdateUserAssignmentSuccess implements Action {
  readonly type: string = PropertyActionTypes.UPDATE_USER_ASSIGNMENT_SUCCESS;
  constructor(public resp: string = '') { }
}
export class BulkReassignProperty implements Action {
  readonly type: string = PropertyActionTypes.BULK_REASSIGN_PROPERTY;
  constructor(public payload: BulkAssignProperty) { }
}
export class BulkReassignPropertySuccess implements Action {
  readonly type: string = PropertyActionTypes.BULK_REASSIGN_PROPERTY_SUCCESS;
  constructor(public resp: string = '') { }
}
export class ArchiveProperty implements Action {
  readonly type: string = PropertyActionTypes.ARCHIVE_PROPERTY;
  constructor(public id: string) { }
}
export class IncreaseShareCount implements Action {
  readonly type: string = PropertyActionTypes.INCREASE_SHARE_COUNT;
  constructor(public id: string) { }
}
export class FetchLocationList implements Action {
  readonly type: string = PropertyActionTypes.FETCH_LOCATION_LIST;
  constructor() { }
}
export class FetchLocationListSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_LOCATION_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class FetchBrochureList implements Action {
  readonly type: string = PropertyActionTypes.FETCH_BROCHURE_LIST;
  constructor(public id: string) { }
}
export class FetchBrochureListSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_BROCHURE_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class UploadBrochure implements Action {
  readonly type: string = PropertyActionTypes.UPLOAD_BROCHURE;
  constructor(public id: string, public payload?: any) { }
}
export class UploadBrochureSuccess implements Action {
  readonly type: string = PropertyActionTypes.UPLOAD_BROCHURE_SUCCESS;
  constructor(public resp?: any) { }
}

export class FetchGalleryDropdownData implements Action {
  readonly type: string = PropertyActionTypes.FETCH_GALLERY_DROPDOWN_DATA;
  constructor() { }
}

export class FetchGalleryDropdownDataSuccess implements Action {
  readonly type: string =
    PropertyActionTypes.FETCH_GALLERY_DROPDOWN_DATA_SUCCESS;
  constructor(public response: Array<String> = []) { }
}

export class UpdateGallery implements Action {
  readonly type: string = PropertyActionTypes.UPDATE_GALLERY;
  constructor(public payload: any) { }
}
export class FetchMatchingLeadsList implements Action {
  readonly type: string = PropertyActionTypes.FETCH_MATCHING_LEADS;
  constructor(public payload: any) { }
}
export class FetchMatchingLeadsListSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_MATCHING_LEADS_LIST_SUCCESS;
  constructor(public response: any) { }
}
export class FetchArchivedPropertyList implements Action {
  readonly type: string = PropertyActionTypes.FETCH_ARCHIVED_PROPERTY_LIST;
  constructor(
    public payload = {
      pageNumber: 1,
      pageSize: 10,
      path: 'property/archived',
    }
  ) { }
}
export class FetchArchivedPropertyListSuccess implements Action {
  readonly type: string =
    PropertyActionTypes.FETCH_ARCHIVED_PROPERTY_LIST_SUCCESS;
  constructor(public response: any) { }
}
export class PropertyExcelUpload implements Action {
  readonly type: string = PropertyActionTypes.PROPERTY_EXCEL_UPLOAD;
  constructor(public file: File) { }
}
export class PropertyExcelUploadSuccess implements Action {
  readonly type: string = PropertyActionTypes.PROPERTY_EXCEL_UPLOAD_SUCCESS;
  constructor(public resp: LeadExcel) { }
}
export class UploadMappedColumns implements Action {
  readonly type: string = PropertyActionTypes.UPLOAD_MAPPED_COLUMNS;
  constructor(public payload: MapColumnsExcel) { }
}
export class FetchPropertyExcelUploadedList implements Action {
  readonly type: string = PropertyActionTypes.FETCH_EXCEL_UPLOADED_LIST;
  constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchPropertyExcelUploadedSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchPropertyExport implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_EXPORT;
  constructor(public payload: any) { }
}
export class FetchPropertyExportSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_EXPORT_SUCCESS;
  constructor(public response: any = []) { }
}

export class ExportProperty implements Action {
  readonly type: string = PropertyActionTypes.EXPORT_PROPERTY;
  constructor(public payload: any) { }
}
export class ExportPropertySuccess implements Action {
  readonly type: string = PropertyActionTypes.EXPORT_PROPERTY_SUCCESS;
  constructor(public resp: string = '') { }
}

export class FetchExportPropertyStatus implements Action {
  readonly type: string = PropertyActionTypes.FETCH_EXPORT_PROPERTY_STATUS;
  constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchExportPropertyStatusSuccess implements Action {
  readonly type: string =
    PropertyActionTypes.FETCH_EXPORT_PROPERTY_STATUS_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchOwnerNames implements Action {
  readonly type: string = PropertyActionTypes.FETCH_OWNER_NAMES;
  constructor() { }
}
export class FetchOwnerNamesSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_OWNER_NAMES_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchMicrositeProperty implements Action {
  readonly type: string = PropertyActionTypes.FETCH_MICROSITE_PROPERTY;
  constructor(public payload: any) { }
}
export class FetchMicrositePropertySuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_MICROSITE_PROPERTY_SUCCESS;
  constructor(public property: any[] = []) { }
}
export class FetchMSSimilarProperties implements Action {
  readonly type: string =
    PropertyActionTypes.FETCH_MICROSITE_SIMILAR_PROPERTIES;
  constructor(public payload: any) { }
}
export class FetchMSSimilarPropertiesSuccess implements Action {
  readonly type: string =
    PropertyActionTypes.FETCH_MICROSITE_SIMILAR_PROPERTIES_SUCCESS;
  constructor(
    public properties: PropertyResponse<Property> = {} as PropertyResponse<Property>
  ) { }
}
export class AddMicrositeLead implements Action {
  readonly type: string = PropertyActionTypes.ADD_MS_LEAD;
  constructor(public payload: any) { }
}
export class AddMicrositeLeadSuccess implements Action {
  readonly type: string = PropertyActionTypes.ADD_MS_LEAD_SUCCESS;
  constructor(public resp: string) { }
}
export class FetchMicrositeUserDetails implements Action {
  readonly type: string = PropertyActionTypes.FETCH_MICROSITE_USER_DETAILS;
  constructor(public payload: any) { }
}
export class FetchMicrositeUserDetailsSuccess implements Action {
  readonly type: string =
    PropertyActionTypes.FETCH_MICROSITE_USER_DETAILS_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchPropertyCurrency implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_CURRENCY_LIST;
  constructor() { }
}
export class FetchPropertyCurrencySuccess implements Action {
  readonly type: string =
    PropertyActionTypes.FETCH_PROPERTY_CURRENCY_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchPropertyWithIdNameList implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_WITH_ID_NAME_LIST;
  constructor() { }
}
export class FetchPropertyWithIdNameListSuccess implements Action {
  readonly type: string =
    PropertyActionTypes.FETCH_PROPERTY_WITH_ID_NAME_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}
export class AddWaterMark implements Action {
  readonly type: string = PropertyActionTypes.ADD_WATER_MARK;
  constructor(public payload: any) { }
}

export class AddWaterMarkSuccess implements Action {
  readonly type: string = PropertyActionTypes.ADD_WATER_MARK_SUCCESS;
  constructor(public response: any) { }
}

export class FetchPropertyCount implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_COUNT;
  constructor(public payload: any) { }
}

export class FetchPropertyCountSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_COUNT_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class FetchPropertyAssignments implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_ASSIGNMENTS;
  constructor(public id: string) { }
}

export class FetchPropertyAssignmentsSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_ASSIGNMENTS_SUCCESS;
  constructor(public response: any) { }
}

export class CloneProperty implements Action {
  readonly type: string = PropertyActionTypes.CLONE_PROPERTY;
  constructor(public id: any) { }
}

export class ClonePropertySuccess implements Action {
  readonly type: string = PropertyActionTypes.CLONE_PROPERTY_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class FetchPropertyModifiedOn implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_MODIFIED_ON;
  constructor(public payload: any) { }
}

export class FetchPropertyModifiedOnSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_MODIFIED_ON_SUCCESS;
  constructor(public modifiedOnData: any) { }
}

export class AddListing implements Action {
  readonly type: string = PropertyActionTypes.ADD_LISTING;
  constructor(public payload: any) { }
}

export class AddListingSuccess implements Action {
  readonly type: string = PropertyActionTypes.ADD_LISTING_SUCCESS;
  constructor(public resp: any) { }
}

export class UpdateListing implements Action {
  readonly type: string = PropertyActionTypes.UPDATE_LISTING;
  constructor(public payload: any, public id: string) { }
}

export class UpdateListingSuccess implements Action {
  readonly type: string = PropertyActionTypes.UPDATE_LISTING_SUCCESS;
  constructor(public resp: any) { }
}

export class FetchListingById implements Action {
  readonly type: string = PropertyActionTypes.FETCH_LISTING_BY_ID;
  constructor(public id: string) { }
}

export class FetchListingByIdSuccess implements Action {
  readonly type: string = PropertyActionTypes.FETCH_LISTING_BY_ID_SUCCESS;
  constructor(public resp: any) { }
}

export class FetchPropertyWithGoogleLocation implements Action {
  readonly type: string = PropertyActionTypes.FETCH_PROPERTY_WITH_GOOGLE_LOCATION;
  constructor() { }
}

export class FetchPropertyWithGoogleLocationSuccess implements Action {
  readonly type: string =
    PropertyActionTypes.FETCH_PROPERTY_WITH_GOOGLE_LOCATION_SUCCESS;
  constructor(public response: any[] = []) { }
}
