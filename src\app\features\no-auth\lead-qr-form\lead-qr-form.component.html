<!-- <div class="flex-between px-20 py-16 bg-white">
    <h4 class="fw-semi-bold">{{'BUTTONS.add-lead' | translate}}</h4>
    <div class="mx-10 text-dark-gray">v{{versionNo}}</div>
</div> -->
<ng-container *ngIf="isTemplateExist else templateNotFound">
    <div class="h-100-64 scrollbar bg-white">
        <div class="m-auto max-w-1260">
            <!-- Header -->
            <div class="border-bottom py-20" *ngIf="qrFormData?.header?.headerDesign"
                [ngStyle]="{ 'background-color': qrFormData?.header?.backgroundColor ? qrFormData?.header?.backgroundColor : '#fff', 'color': qrFormData?.header?.textColor ? qrFormData?.header?.textColor : '#000' }">
                <ng-container *ngIf="qrFormData?.header?.headerDesign === 1">
                    <div class="justify-center" *ngIf="qrFormData?.header?.logoUrl">
                        <img [appImage]="qrFormData?.header?.logoUrl ? s3BucketUrl+qrFormData?.header?.logoUrl : ''"
                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border mb-16" width="80"
                            height="80">
                    </div>
                    <h2 class="text-center">{{ qrFormData?.companyName }}
                    </h2>
                </ng-container>
                <ng-container *ngIf="qrFormData?.header?.headerDesign === 2">
                    <h2 class="text-center">{{ qrFormData?.companyName }}
                    </h2>
                    <div class="justify-center mt-16" *ngIf="qrFormData?.header?.logoUrl">
                        <img [appImage]="qrFormData?.header?.logoUrl ? s3BucketUrl+qrFormData?.header?.logoUrl : ''"
                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80" height="80">
                    </div>
                </ng-container>
                <ng-container *ngIf="qrFormData?.header?.headerDesign === 3">
                    <div class="flex-between mx-20">
                        <h2 class="text-center">{{ qrFormData?.companyName }}
                        </h2>
                        <div class="justify-center" *ngIf="qrFormData?.header?.logoUrl">
                            <img [appImage]="qrFormData?.header?.logoUrl ? s3BucketUrl+qrFormData?.header?.logoUrl : ''"
                                [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80"
                                height="80">
                        </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="qrFormData?.header?.headerDesign === 4">
                    <div class="flex-between mx-20">
                        <div class="justify-center">
                            <img *ngIf="qrFormData?.header?.logoUrl"
                                [appImage]="qrFormData?.header?.logoUrl ? s3BucketUrl+qrFormData?.header?.logoUrl : ''"
                                [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80"
                                height="80">
                        </div>
                        <h2 class="text-center">{{ qrFormData?.companyName }}
                        </h2>
                        <div></div>
                    </div>
                </ng-container>
                <ng-container *ngIf="qrFormData?.header?.headerDesign === 5">
                    <div class="flex-between mx-20">
                        <div class="justify-center">
                            <img [appImage]="qrFormData?.header?.logoUrl ? s3BucketUrl+qrFormData?.header?.logoUrl : ''"
                                [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80"
                                height="80">
                        </div>
                        <h2 class="text-center">{{ qrFormData?.companyName }}
                        </h2>
                    </div>
                </ng-container>
                <ng-container *ngIf="qrFormData?.header?.headerDesign === 6">
                    <h2 class="text-center">{{ qrFormData?.companyName }}
                    </h2>
                </ng-container>
            </div>
            <form [formGroup]="addLeadForm" autocomplete="off" class="bg-white py-30 pr-10 pl-30">
                <h3 class="mb-30 fw-600 text-center">Enquiry Form</h3>
                <h5 class="fw-600 py-10 border-bottom">Basic Info</h5>
                <div class="d-flex flex-wrap w-100">
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('name') !== -1">
                        <div class="mr-20">
                            <div class="field-label-req">{{'GLOBAL.name' | translate}}</div>
                            <form-errors-wrapper [control]="addLeadForm.controls['name']"
                                label="{{'GLOBAL.name' | translate }}" class="position-relative">
                                <input type="text" required formControlName="name" id="inpLeadName"
                                    data-automate-id="inpLeadName" placeholder="ex. Mounika Pampana" maxlength="75">
                                <p class="position-absolute right-4 bottom-0">
                                    {{ addLeadForm.controls['name']?.value?.length }}/75</p>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="position-relative w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('phoneNo') !== -1">
                        <div class="mr-20">
                            <div class="field-label-req">{{'INTEGRATION.primary' | translate}}
                                {{'GLOBAL.number' | translate}}</div>
                            <form-errors-wrapper [control]="addLeadForm.controls['contactNo']" label="Primary Number">
                                <div *ngIf="!resetNumber">
                                    <ngx-mat-intl-tel-input #contactNoInput *ngIf="hasInternationalSupport"
                                        [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                                        [enableSearch]="true" formControlName="contactNo"
                                        class="no-validation contactNoInput">
                                    </ngx-mat-intl-tel-input>
                                    <ngx-mat-intl-tel-input #contactNoInput *ngIf="!hasInternationalSupport"
                                        [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                                        [enablePlaceholder]="true" [enableSearch]="true" formControlName="contactNo"
                                        class="no-validation contactNoInput">
                                    </ngx-mat-intl-tel-input>
                                </div>
                            </form-errors-wrapper>
                            <!-- <div *ngIf="checkDuplicacy && numberEdited && addLeadForm?.controls?.['contactNo']?.status === 'VALID'"
                            class="mt-4 text-xs text-red position-absolute right-20 fw-semi-bold">
                            {{ 'CONTACT.already-exist' | translate }}
                        </div> -->
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('altPhoneNo') !== -1">
                        <div class="mr-20">
                            <div class="field-label">{{'GLOBAL.alternate' | translate}}
                                {{'GLOBAL.number' | translate}}</div>
                            <form-errors-wrapper [control]="addLeadForm.controls['alternateContactNo']"
                                label="{{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' | translate}}">
                                <div *ngIf="!resetNumber">
                                    <ngx-mat-intl-tel-input #alternateNoInput *ngIf="hasInternationalSupport"
                                        [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                                        [enableSearch]="true" formControlName="alternateContactNo"
                                        class="no-validation alternateNoInput">
                                    </ngx-mat-intl-tel-input>
                                    <ngx-mat-intl-tel-input #alternateNoInput *ngIf="!hasInternationalSupport"
                                        [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                                        [enablePlaceholder]="true" [enableSearch]="true"
                                        formControlName="alternateContactNo" class="no-validation alternateNoInput">
                                    </ngx-mat-intl-tel-input>
                                </div>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('landLine') !== -1">
                        <div class="mr-20">
                            <div class="field-label">Landline Number</div>
                            <form-errors-wrapper [control]="addLeadForm.controls['landLine']"
                                label="Landline Number">
                                <input type="text" formControlName="landLine" (keypress)="allowLandlineInput($event)" id="inpLandlineNumber"
                                    data-automate-id="inpLandlineNumber" placeholder="ex. 022-25846975">
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('email') !== -1">
                        <div class="mr-20">
                            <div class="field-label">{{'USER.email' | translate}}</div>
                            <form-errors-wrapper [control]="addLeadForm.controls['email']"
                                label="{{'USER.email' | translate}}">
                                <input type="email" formControlName="email" id="inpLeadMail"
                                    data-automate-id="inpLeadMail" placeholder="ex. <EMAIL>">
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
                <h5 class="fw-600 py-10 border-bottom mt-40" *ngIf="shouldDisplayEnquiryInfo()">More Information</h5>
                <div class="d-flex flex-wrap">
                    <div class="ph-w-100 w-50" *ngIf="selectedFieldNames?.indexOf('referralName') !== -1">
                        <div class="field-label">{{'LEAD_FORM.referral-name' | translate}}</div>
                        <div class="form-group mr-20">
                            <input type="text" formControlName="referralName" id="inpReferralName"
                                data-automate-id="inpReferralName" placeholder="enter referral name">
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('referralContactNo') !== -1">
                        <div class="field-label text-nowrap">{{'LEAD_FORM.referral-phone-no' | translate}}</div>
                        <div class="mr-20">
                            <form-errors-wrapper [control]="addLeadForm.controls['referralContactNo']"
                                label="{{'LEAD_FORM.referral-phone-no' | translate}}">
                                <div *ngIf="!resetNumber">
                                    <ngx-mat-intl-tel-input #referralNoInput *ngIf="hasInternationalSupport"
                                        [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                                        [enableSearch]="true" formControlName="referralContactNo"
                                        class="no-validation referralNoInput">
                                    </ngx-mat-intl-tel-input>
                                    <ngx-mat-intl-tel-input #referralNoInput *ngIf="!hasInternationalSupport"
                                        [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                                        [enablePlaceholder]="true" [enableSearch]="true"
                                        formControlName="referralContactNo" class="no-validation referralNoInput">
                                    </ngx-mat-intl-tel-input>
                                </div>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('referralEmail') !== -1">
                        <div class="mr-20">
                            <div class="field-label">Referral {{'USER.email' |
                                translate}}</div>
                            <form-errors-wrapper [control]="addLeadForm.controls['referralEmail']"
                                label="Referral {{'USER.email' | translate}}">
                                <input type="email" formControlName="referralEmail" id="inpReferralMail"
                                    data-automate-id="inpReferralMail" placeholder="enter referral email">
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('customerLocality') !== -1">
                        <div class="field-label">Customer Locality</div>
                        <div class="form-group mr-20">
                            <input type="text" formControlName="customerLocality" placeholder="enter locality">
                        </div>
                    </div>
                    <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('customerSubCommunity') !== -1">
                            <div class="field-label">Customer Sub-Community</div>
                            <div class="form-group mr-20 mt-0">
                                <input type="text" formControlName="customerSubCommunity"
                                    placeholder="enter sub-community">
                            </div>
                        </div>
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('customerCommunity') !== -1">
                            <div class="field-label">Customer Community</div>
                            <div class="form-group mr-20 mt-0">
                                <input type="text" formControlName="customerCommunity" placeholder="enter community">
                            </div>
                        </div>
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('customerTowerName') !== -1">
                            <div class="field-label">Customer Tower Name</div>
                            <div class="form-group mr-20 mt-0">
                                <input type="text" formControlName="customerTowerName" placeholder="enter tower name">
                            </div>
                        </div>
                    </ng-container>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('customerCity') !== -1">
                        <div class="field-label">Customer City</div>
                        <div class="form-group mr-20">
                            <input type="text" formControlName="customerCity" placeholder="enter city">
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('customerState') !== -1">
                        <div class="field-label">Customer State</div>
                        <div class="form-group mr-20">
                            <input type="text" formControlName="customerState" placeholder="enter state">
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('customerCountry') !== -1">
                        <div class="mr-20">
                            <div class="field-label">Customer Country</div>
                            <div class="form-group">
                                <input type="text" formControlName="customerCountry" placeholder="enter country">
                            </div>
                        </div>
                    </div>
                    <div class="align-center w-50 ph-w-100 qr-code"
                        *ngIf="selectedFieldNames?.indexOf('budget') !== -1">
                        <div class="field-rupees-tag w-100">
                            <div class="field-label">Budget</div>
                            <div class="align-center w-100">
                                <div class="position-relative w-50 budget-dropdown">
                                    <form-errors-wrapper [control]="addLeadForm.controls['lowerBudget']"
                                        label="Min. {{'LABEL.budget' | translate}}">
                                        <input type="number" formControlName="lowerBudget" min="1"
                                            id="inpLeadLowerBudget" data-automate-id="inpLeadLowerBudget"
                                            placeholder="ex. 4000000" maxlength="10" (keydown)="onlyNumbers($event)">
                                        <div class="no-validation">
                                            <ng-container *ngIf="currency?.length > 1; else showCurrencySymbol">
                                                <ng-select formControlName="currency"
                                                    class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                                    <ng-option *ngFor="let curr of currency" [value]="curr.currency">
                                                        <span [title]="curr.currency">
                                                            {{curr.currency}}
                                                        </span>
                                                    </ng-option>
                                                </ng-select>
                                            </ng-container>
                                            <ng-template #showCurrencySymbol>
                                                <h5 class="rupees px-12 py-8 fw-600 m-4">{{ defaultCurrency }}</h5>
                                            </ng-template>
                                        </div>
                                    </form-errors-wrapper>
                                    <div *ngIf="addLeadForm.controls['lowerBudget'].value"
                                        class="position-absolute right-5 nbottom-15 text-accent-green fw-semi-bold text-sm">
                                        {{lowerBudgetInWords}}
                                    </div>
                                </div>
                                <div class="mx-10">to</div>
                                <div class="position-relative w-50">
                                    <div class="mr-20 budget-dropdown">
                                        <form-errors-wrapper [control]="addLeadForm.controls['upperBudget']"
                                            label="Max. {{'LABEL.budget' | translate}}">
                                            <input type="number" formControlName="upperBudget" min="1"
                                                id="inpLeadUpperBudget" data-automate-id="inpLeadUpperBudget"
                                                placeholder="ex. 4000000" maxlength="10"
                                                (keydown)="onlyNumbers($event)">
                                            <div class="no-validation">
                                                <ng-container *ngIf="currency?.length > 1; else showCurrencySymbol">
                                                    <ng-select formControlName="currency"
                                                        class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                                        <ng-option *ngFor="let curr of currency"
                                                            [value]="curr.currency">
                                                            <span [title]="curr.currency">
                                                                {{curr.currency}}
                                                            </span>
                                                        </ng-option>
                                                    </ng-select>
                                                </ng-container>
                                            </div>
                                        </form-errors-wrapper>
                                        <div *ngIf="addLeadForm.controls['upperBudget'].value"
                                            class="position-absolute right-20 nbottom-15 text-accent-green fw-semi-bold text-sm">
                                            {{upperBudgetInWords}}
                                        </div>
                                        <div *ngIf="budgetValidation"
                                            class="mt-12 text-xs text-red position-absolute right-20 fw-semi-bold">
                                            {{ 'LEADS.budget-validation' | translate }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('carpetArea') !== -1">
                        <div [ngClass]="addLeadForm.controls['carpetArea'].value ? 'field-label-req' : 'field-label'">
                            Carpet Area</div>
                        <div class="align-center mr-20">
                            <div class="w-50">
                                <div class="mr-20">
                                    <form-errors-wrapper [control]="addLeadForm.controls['carpetArea']"
                                        label="Carpet Area">
                                        <input type="number" min="0" placeholder="ex. 1906"
                                            formControlName="carpetArea">
                                    </form-errors-wrapper>
                                </div>
                            </div>
                            <div class="w-50">
                                <form-errors-wrapper [control]="addLeadForm.controls['carpetAreaUnitId']"
                                    label="Carpet Area Unit">
                                    <ng-select [virtualScroll]="true" [items]="areaSizeUnits" class="bg-white"
                                        formControlName="carpetAreaUnitId" placeholder="ex. sq. feet."
                                        [readonly]="addLeadForm.controls['carpetArea']?.value ? false : true"
                                        bindValue="id" bindLabel="unit"
                                        (change)="onUnitChange('carpetAreaUnitId')"></ng-select>
                                </form-errors-wrapper>
                            </div>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('builtUpArea') !== -1">
                        <div [ngClass]="addLeadForm.controls['builtUpArea'].value ? 'field-label-req' : 'field-label'">
                            Built-up Area</div>
                        <div class="align-center mr-20">
                            <div class="w-50">
                                <div class="mr-20">
                                    <form-errors-wrapper [control]="addLeadForm.controls['builtUpArea']"
                                        label=" Built-up Area">
                                        <input type="number" min="0" placeholder="ex. 1901"
                                            formControlName="builtUpArea">
                                    </form-errors-wrapper>
                                </div>
                            </div>
                            <div class="w-50">
                                <form-errors-wrapper [control]="addLeadForm.controls['builtUpAreaUnitId']"
                                    label="Built-up Area Unit">
                                    <ng-select [virtualScroll]="true" [items]="areaSizeUnits" class="bg-white"
                                        formControlName="builtUpAreaUnitId" placeholder="ex. sq. feet."
                                        [readonly]="addLeadForm.controls['builtUpArea']?.value ? false : true"
                                        bindValue="id" bindLabel="unit"
                                        (change)="onUnitChange('builtUpAreaUnitId')"></ng-select>
                                </form-errors-wrapper>
                            </div>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('saleableArea') !== -1">
                        <div class="mr-20">
                            <div
                                [ngClass]="addLeadForm.controls['saleableArea'].value ? 'field-label-req' : 'field-label'">
                                Saleable Area</div>
                            <div class="align-center">
                                <div class="w-50">
                                    <div class="mr-20">
                                        <form-errors-wrapper [control]="addLeadForm.controls['saleableArea']"
                                            label="Saleable Area">
                                            <input type="number" min="0" placeholder="ex. 506"
                                                formControlName="saleableArea">
                                        </form-errors-wrapper>
                                    </div>
                                </div>
                                <div class="w-50">
                                    <form-errors-wrapper [control]="addLeadForm.controls['saleableAreaUnitId']"
                                        label="Saleable Area Unit">
                                        <ng-select [virtualScroll]="true" formControlName="saleableAreaUnitId"
                                            ResizableDropdown placeholder="ex. sq. feet."
                                            [readonly]="addLeadForm.controls['saleableArea']?.value ? false : true"
                                            bindValue="id" bindLabel="unit" [items]="areaSizeUnits" class="bg-white"
                                            (change)="onUnitChange('saleableAreaUnitId')"></ng-select>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('propertyArea') !== -1">
                            <div class="mr-20">
                                <div
                                    [ngClass]="addLeadForm.controls['propertyArea'].value ? 'field-label-req' : 'field-label'">
                                    Property Area</div>
                                <div class="align-center">
                                    <div class="w-50">
                                        <div class="mr-20">

                                            <form-errors-wrapper [control]="addLeadForm.controls['propertyArea']"
                                                label="Property Area">
                                                <input type="number" min="0" placeholder="ex. 196"
                                                    formControlName="propertyArea">
                                            </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <div class="w-50">
                                        <form-errors-wrapper [control]="addLeadForm.controls['propertyAreaUnitId']"
                                            label="Property Area Unit">
                                            <ng-select [virtualScroll]="true" formControlName="propertyAreaUnitId"
                                                ResizableDropdown placeholder="ex. sq. feet."
                                                [readonly]="addLeadForm.controls['propertyArea']?.value ? false : true"
                                                bindValue="id" bindLabel="unit" [items]="areaSizeUnits" class="bg-white"
                                                (change)="onUnitChange('propertyAreaUnitId')"></ng-select>
                                        </form-errors-wrapper>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('netArea') !== -1">
                            <div class="mr-20">
                                <div
                                    [ngClass]="addLeadForm.controls['netArea'].value ? 'field-label-req' : 'field-label'">
                                    Net Area</div>
                                <div class="align-center">
                                    <div class="w-50">
                                        <div class="mr-20">
                                            <form-errors-wrapper [control]="addLeadForm.controls['netArea']"
                                                label="Net Area">
                                                <input type="number" min="0" placeholder="ex. 916"
                                                    formControlName="netArea"> </form-errors-wrapper>
                                        </div>
                                    </div>
                                    <div class="w-50">
                                        <form-errors-wrapper [control]="addLeadForm.controls['netAreaUnitId']"
                                            label="Net Area Unit">
                                            <ng-select [virtualScroll]="true" formControlName="netAreaUnitId"
                                                ResizableDropdown placeholder="ex. sq. feet."
                                                [readonly]="addLeadForm.controls['netArea']?.value ? false : true"
                                                bindValue="id" bindLabel="unit" [items]="areaSizeUnits" class="bg-white"
                                                (change)="onUnitChange('netAreaUnitId')"></ng-select>
                                        </form-errors-wrapper>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('unitName') !== -1">
                            <div class="mr-20">
                                <div class="field-label">Unit Number/Name</div>
                                <div class="form-group">
                                    <input type="text" formControlName="unitName" id="inpLeadunitNumber"
                                        data-automate-id="inpLeadunitNumber" placeholder="enter unit number/name">
                                </div>
                            </div>
                        </div>
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('clusterName') !== -1">
                            <div class="mr-20">
                                <div class="field-label">Cluster Name</div>
                                <div class="form-group">
                                    <input type="text" formControlName="clusterName" id="inpLeadClusterName"
                                        data-automate-id="inpLeadClusterName" placeholder="enter cluster name">
                                </div>
                            </div>
                        </div>
                    </ng-container>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('enquiredFor') !== -1">
                        <div class="field-label">{{'LEAD_FORM.enquired-for' | translate }}</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="enquiredForList" [clearSearchOnAdd]="true"
                                [multiple]="true" [closeOnSelect]="false" [multiple]="true" [closeOnSelect]="false"
                                placeholder="{{'GLOBAL.select' | translate}} enquired for" formControlName="enquiredFor"
                                class="bg-white" [bindLabel]="'type'" [bindValue]="'type'">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>{{item.type}}
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('purpose') !== -1">
                        <div class="mr-20">
                            <div class="field-label">Purpose</div>
                            <div class="form-group">
                                <ng-select [virtualScroll]="true" [items]="purposeList" ResizableDropdown
                                    placeholder="Select Offering Type" formControlName="purpose" class="bg-white"
                                    bindLabel="displayName" bindValue="value">
                                </ng-select>
                            </div>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('basePropertyType') !== -1">
                        <div class="field-label">{{'LABEL.property' | translate }} {{'LABEL.type' | translate }}
                        </div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="PropertyType" [closeOnSelect]="true"
                                placeholder="{{'GLOBAL.select' | translate}} property type"
                                formControlName="propertyTypeId" class="bg-white">
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100 remove-form-group"
                        *ngIf="selectedFieldNames?.indexOf('subPropertyType') !== -1">
                        <div
                            [ngClass]="addLeadForm.controls['propertyTypeId'].value ? 'field-label-req' : 'field-label'">
                            {{'PROPERTY.sub-type' | translate}}</div>
                        <div class="mr-20">
                            <form-errors-wrapper [control]="addLeadForm.controls['propSubType']"
                                label="{{'PROPERTY.sub-type' | translate}}">
                                <ng-select [virtualScroll]="true" [closeOnSelect]="false"
                                    placeholder="{{ 'GLOBAL.select' | translate }} property sub-type" [multiple]="true"
                                    [items]="propSubTypes" bindValue="displayName" bindLabel="displayName"
                                    formControlName="propSubType" class="bg-white"
                                    [readonly]="addLeadForm.controls['propertyTypeId'].value ? false: true">
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container "><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span><span
                                                class="text-truncate-1 break-all">{{item?.displayName}}</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <ng-container *ngIf="!globalSettingsDetails?.isCustomLeadFormEnabled">
                        <!-- *ngIf="isResidential && addLeadForm.controls['propSubType'].value != 'Plot'" -->
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('noOfBHK') !== -1">
                            <div class="field-label">{{'PROPERTY.bhk' | translate }}</div>
                            <div class="mr-20">
                                <ng-select [virtualScroll]="true" [items]="noOfBhk" [clearSearchOnAdd]="true"
                                    [multiple]="true" [closeOnSelect]="false" [multiple]="true" [closeOnSelect]="false"
                                    placeholder="{{'GLOBAL.select' | translate}}" formControlName="noOfBHK"
                                    class="bg-white">
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span>{{item}}
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('bhkType') !== -1">
                            <div class="field-label">{{'PROPERTY.bhk' | translate }} {{'LABEL.type' | translate}}
                            </div>
                            <div class="mr-20">
                                <ng-select [virtualScroll]="true" [items]="bhkTypes" [clearSearchOnAdd]="true"
                                    [multiple]="true" [closeOnSelect]="false" [multiple]="true" [closeOnSelect]="false"
                                    placeholder="{{'GLOBAL.select' | translate}}" formControlName="bhkType"
                                    class="bg-white">
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span>{{item}}
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                        <!-- addLeadForm.controls['propertyTypeId'].value -->
                        <!-- *ngIf="addLeadForm.get('propertyTypeId').value === 'Residential' && addLeadForm.controls['propSubType'].value != 'Plot'" -->
                        <!-- <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('noOfBHK') !== -1">
                            <div class="mr-20">
                                <div class="field-label">BR</div>
                                <ng-select [virtualScroll]="true" [items]="numbers10" [multiple]="true"
                                    [closeOnSelect]="false" ResizableDropdown class="bg-white" bindLabel="display"
                                    bindValue="value" formControlName="noOfBHK" placeholder="select BR">
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span><span class="text-truncate-1 break-all">
                                                {{item.display}}</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div> -->
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('baths') !== -1">
                            <div class="mr-20">
                                <div class="field-label">Baths</div>
                                <ng-select [virtualScroll]="true" [items]="numbers10" [multiple]="true"
                                    [closeOnSelect]="false" ResizableDropdown class="bg-white" bindLabel="display"
                                    bindValue="value" formControlName="baths" placeholder="select baths">
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span><span class="text-truncate-1 break-all">
                                                {{item.display}}</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('beds') !== -1">
                            <div class="mr-20">
                                <div class="field-label">Beds</div>
                                <ng-select [virtualScroll]="true" [items]="numbers" [multiple]="true"
                                    [closeOnSelect]="false" ResizableDropdown class="bg-white" bindLabel="display"
                                    bindValue="value" formControlName="beds" placeholder="select beds">
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span><span class="text-truncate-1 break-all">
                                                {{item.display}}</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                        <ng-container *ngIf="selectedFieldNames?.indexOf('preferredFloors') !== -1">
                            <!-- addLeadForm.get('propertyTypeId')?.value !== 'Agricultural' &&  -->
                            <div class="w-50 ph-w-100">
                                <div class="mr-20">
                                    <div class="field-label">Preferred Floor(s)</div>
                                    <ng-select [virtualScroll]="true" [items]="floorOptions" [multiple]="true"
                                        [closeOnSelect]="false" ResizableDropdown formControlName="preferredFloors"
                                        placeholder="select preferred floor" class="bg-white">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span><span class="text-truncate-1 break-all">
                                                    {{item}}</span>
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                </div>
                            </div>
                        </ng-container>
                        <ng-container *ngIf="selectedFieldNames?.indexOf('furnishStatus') !== -1 ">
                            <!-- addLeadForm.get('propertyTypeId')?.value !== 'Agricultural' &&  -->
                            <div class="w-50 ph-w-100">
                                <div class="mr-20">
                                    <div class="field-label">
                                        {{'PROPERTY.PROPERTY_DETAIL.furnish-status' |
                                        translate }}</div>
                                    <form-errors-wrapper [control]="addLeadForm.controls['furnishStatus']"
                                        label="{{'PROPERTY.PROPERTY_DETAIL.furnish-status' | translate }}">
                                        <ng-select [virtualScroll]="true" ResizableDropdown class="bg-white"
                                            placeholder="select furnish status" [items]="furnishStatusList"
                                            bindValue="dispName" bindLabel="dispName" formControlName="furnishStatus">
                                        </ng-select>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                        </ng-container>
                    </ng-container>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('project') !== -1">
                        <div class="field-label">{{ 'SIDEBAR.project' | translate }}</div>
                        <div class="form-group mr-20">
                            <!-- <input type="text" formControlName="project" placeholder="ex. abc"> -->
                            <ng-select [virtualScroll]="true" [items]="projectList" [closeOnSelect]="true"
                                bindLabel="name" bindValue="id" name="projectsList" formControlName="projectId"
                                class="bg-white" placeholder="select/create project">
                            </ng-select>

                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('property') !== -1">
                        <div class="field-label">Property</div>
                        <div class="form-group mr-20">
                            <input type="text" formControlName="property" placeholder="enter project">
                            <!-- <ng-select [virtualScroll]="true" [items]="propertyList" [multiple]="true"
                            [closeOnSelect]="false" [addTag]="true" bindLabel="property" bindValue="property"
                            name="propertiesList" formControlName="propertiesList" addTagText="Create New Property"
                            placeholder="{{ 'GLOBAL.select' | translate }}/Create Property" class="bg-white">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span>{{item}}
                                </div>
                            </ng-template>
                        </ng-select> -->
                        </div>
                    </div>
                    <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('offeringType') !== -1">
                            <div class="mr-20">
                                <div class="field-label">Offering Type</div>
                                <div class="form-group">
                                    <ng-select [virtualScroll]="true" [items]="offerType" ResizableDropdown
                                        placeholder="Select Offering Type" formControlName="offeringType"
                                        class="bg-white" bindLabel="displayName" bindValue="value">
                                    </ng-select>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('agencyName') !== -1">
                        <div class="field-label">Agency Name</div>
                        <div class="form-group mr-20">
                            <ng-select [virtualScroll]="true" [items]="agencyNameList" ResizableDropdown
                                placeholder="Select agency name" formControlName="agencies" class="bg-white">
                            </ng-select>
                            <!-- <input type="text" formControlName="agencies" id="inpLeadAgencyName"
                                data-automate-id="inpLeadAgencyName" placeholder="enter agency name"> -->
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('campaigns') !== -1">
                        <div class="field-label">Campaign Name</div>
                        <div class="form-group mr-20">
                            <ng-select [virtualScroll]="true" [items]="campaignList" ResizableDropdown
                                placeholder="Select campaign name" formControlName="campaigns" class="bg-white">
                            </ng-select>
                            <!-- <input type="text" formControlName="campaigns" id="inpLeadcampaigns"
                                data-automate-id="inpLeadcampaigns" placeholder="enter campaign name"> -->
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('channelPartnerName') !== -1">
                        <div class="field-label">{{'LEAD_FORM.channel-partner-name' | translate}}</div>
                        <div class="form-group mr-20">
                            <ng-select [virtualScroll]="true" [items]="channelPartnerList" ResizableDropdown
                                placeholder="Select channel partner name" formControlName="channelPartnerList"
                                class="bg-white">
                            </ng-select>
                            <!-- <input type="text" formControlName="channelPartnerList"
                                placeholder="enter channel partner name"> -->
                            <!-- <ng-select [virtualScroll]="true" [items]="channelPartnerList" [multiple]="true"
                            [closeOnSelect]="false" [addTag]="true" addTagText="Create New Channel Partner"
                            formControlName="channelPartnerList"
                            placeholder="{{ 'GLOBAL.select' | translate }}/Create Channel Partner" class="bg-white">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span>{{item}}
                                </div>
                            </ng-template>
                        </ng-select> -->
                        </div>
                    </div>

                    <div class="w-50 ph-w-100"
                        *ngIf="selectedFieldNames?.indexOf('possessionAvailability') !== -1">
                        <div class="mr-12">
                            <div class="field-label">Possession Needed By</div>
                            <div class="form-group field-rupees-tag">
                                <span>
                                    <div class="rupees icon ic-calendar ic-xxs ic-coal cursor-pointer"
                                        (click)="isOpenPossessionModal = !isOpenPossessionModal">
                                    </div>
                                    <input type="text" id="inpPossessionDate" placeholder="ex. 29/06/2025" readonly
                                        [value]="selectedPossession ? selectedPossession : (addLeadForm.value.globalRange || '')"
                                        (click)="isOpenPossessionModal = !isOpenPossessionModal" autocomplete="off">
                                </span>
                            </div>
                            <div class="position-relative">
                                <div class="position-absolute top-0 w-100 bg-white z-index-1001 box-shadow-10"
                                    *ngIf="isOpenPossessionModal">
                                    <div class="d-flex">
                                        <div class="w-100 bg-white">
                                            <ng-container *ngFor="let type of dateFilterList">
                                                <div class="form-check form-check-inline w-fit-content">
                                                    <input type="radio" id="inpShowData{{type.value}}"
                                                        name="globalRange" formControlName="globalRange"
                                                        [value]="type.value"
                                                        class="radio-check-input w-10 h-10">
                                                    <label class="text-dark-gray text-large ml-8"
                                                        for="inpShowData{{type.value}}">{{type.displayName}}</label>
                                                </div>
                                            </ng-container>
                                            <div class="position-relative dashboard-filter form-group m-6 border py-6"
                                                [ngClass]="{'pe-none disabled' : addLeadForm.controls['globalRange'].value !== 'Custom Date', 'border-red-30': isValidPossDate, 'border': !isValidPossDate}">
                                                <form-errors-wrapper [control]="addLeadForm.controls['globalDate']"
                                                    label="Date">
                                                    <span *ngIf="selectedMonth"
                                                        class="fw-700 text-large text-black-200 px-12 w-90pr cursor-pointer"
                                                        [owlDateTimeTrigger]="dt5">
                                                        {{selectedMonth}} {{selectedYear}}
                                                    </span>
                                                    <span *ngIf="!selectedMonth"
                                                        class="text-dark-gray px-12 w-90pr cursor-pointer"
                                                        [owlDateTimeTrigger]="dt5">
                                                        select month and year
                                                    </span>
                                                    <input type="text" [value]="selectedMonthAndYear"
                                                        [owlDateTimeTrigger]="dt5" [owlDateTime]="dt5"
                                                        placeholder="Select date" class="p-0 border-0 border-remove"
                                                        style="height: 0 !important; opacity: 0; position: absolute; top: 0; left: 0; pointer-events: none;" />
                                                    <owl-date-time #dt5 startView="year" [yearOnly]="true"
                                                        [pickerType]="'calendar'"
                                                        (afterPickerOpen)="onPickerOpened(currentDate, 'month')"
                                                        (monthSelected)="monthChanged($event)"></owl-date-time>
                                                    <div *ngIf="isValidPossDate"
                                                        class="mt-8 text-xs text-red position-absolute right-16 fw-semi-bold">
                                                        Please select possession date
                                                    </div>
                                                </form-errors-wrapper>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-end p-6 border-top">
                                        <div class="btn-coal" (click)="closeModal()">Close</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('locality') !== -1">
                        <div class="field-label">Enquired Locality</div>
                        <div class="form-group mr-20 mt-0">
                            <input type="text" formControlName="enquiredLocality" placeholder="enter locality">
                        </div>
                    </div>
                    <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('enquiredSubCommunity') !== -1">
                            <div class="field-label">Enquired Sub-Community</div>
                            <div class="form-group mr-20 mt-0">
                                <input type="text" formControlName="enquiredSubCommunity"
                                    placeholder="enter sub-community">
                            </div>
                        </div>
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('enquiredCommunity') !== -1">
                            <div class="field-label">Enquired Community</div>
                            <div class="form-group mr-20 mt-0">
                                <input type="text" formControlName="enquiredCommunity" placeholder="enter community">
                            </div>
                        </div>
                        <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('enquiredTowerName') !== -1">
                            <div class="field-label">Enquired Tower Name</div>
                            <div class="form-group mr-20 mt-0">
                                <input type="text" formControlName="enquiredTowerName" placeholder="enter tower name">
                            </div>
                        </div>
                    </ng-container>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('city') !== -1">
                        <div class="field-label">{{'INTEGRATION.enquired-city' | translate}}</div>
                        <div class="form-group mr-20">
                            <input type="text" formControlName="enquiredCity" placeholder="enter enquired city">
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('state') !== -1">
                        <div class="field-label">{{'INTEGRATION.enquired-state' | translate}}</div>
                        <div class="form-group mr-20">
                            <input type="text" formControlName="enquiredState" placeholder="enter state">
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('enquiredCountry') !== -1">
                        <div class="field-label">Enquired Country</div>
                        <div class="form-group mr-20">
                            <input type="text" formControlName="enquiredCountry" placeholder="enter country">
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('profession') !== -1">
                        <div class="field-label">{{'LEADS.profession' | translate}}</div>
                        <div class="form-group mr-20">
                            <ng-select [virtualScroll]="true" [items]="profession" [closeOnSelect]="true"
                                placeholder="select profession" formControlName="profession" class="bg-white">
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('companyName') !== -1">
                        <div class="field-label">{{'AUTH.company-name' | translate}}</div>
                        <div class="form-group mr-20">
                            <input type="text" formControlName="companyName" id="inpLeadCompanyName"
                                data-automate-id="inpLeadCompanyName" placeholder="enter company name">
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('designation') !== -1">
                        <div class="field-label">Designation</div>
                        <div class="form-group mr-20">
                            <input type="text" formControlName="designation" id="inpLeadDesignation"
                                data-automate-id="inpLeadDesignation" placeholder="enter designation">
                        </div>
                    </div>
                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('nationality') !== -1">
                        <div class="mr-20">
                            <div class="field-label">Nationality</div>
                            <ng-select formControlName="nationality" placeholder="select nationality"
                                [closeOnSelect]="true" [virtualScroll]="true" class="bg-white" ResizableDropdown>
                                <ng-option *ngFor="let item of nationalities" [value]="item?.name">
                                    {{ item?.name }}
                                </ng-option>
                            </ng-select>
                        </div>
                    </div>


                    <div class="w-50 ph-w-100" *ngIf="selectedFieldNames?.indexOf('notes') !== -1">
                        <div class="field-label">{{'TASK.notes'| translate}}</div>
                        <div class="form-group mr-20">
                            <textarea rows="4" id="txtLeadNotes" data-automate-id="txtLeadNotes"
                                placeholder="ex. I want to say .... " formControlName="notes"></textarea>
                        </div>
                    </div>
                </div>
            </form>
            <!-- Footer -->
            <div class="p-30 border-top" *ngIf="qrFormData?.footer?.footerDesign"
                [ngStyle]="{ 'background-color': qrFormData?.footer?.backgroundColor ? qrFormData?.footer?.backgroundColor : '#fff', 'color': qrFormData?.footer?.textColor ? qrFormData?.footer?.textColor : '#000' }">
                <ng-container *ngIf="qrFormData?.footer?.footerDesign === 1  && screen?.innerWidth > 480">
                    <div class="px-20 justify-between">
                        <div class="w-25">
                            <div class="d-flex ph-flex-col" *ngIf="qrFormData?.address">
                                <span class="fw-600 mr-8">Address</span>
                                <span class="text-xs word-break fw-semi-bold mt-2">{{qrFormData?.address}}
                                </span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.phoneNumber">
                                <span class="fw-600 mr-8">Phone</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.phoneNumber}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.email">
                                <span class="fw-600 mr-8 text-nowrap">Email</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.email}}</span>
                            </div>
                        </div>
                        <div class="justify-center-col align-center-col w-50">
                            <img *ngIf="qrFormData?.footer?.logoUrl"
                                [appImage]="qrFormData?.footer?.logoUrl ? s3BucketUrl+qrFormData?.footer?.logoUrl : ''"
                                [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80"
                                height="80">
                            <div class="text-center text-xs   mt-10"> © {{currentYear}} {{subDomain}} All
                                Rights Reserved </div>
                        </div>
                        <div class="w-25 align-items-end justify-center">
                            <div class="ph-flex-col align-center">
                                <ng-container *ngFor="let media of socialMedia">
                                    <a *ngIf="hasSocialMedia(media.name)"
                                        [href]="media?.baseUrl + getSocialMediaLink(media.name)" target="_blank">
                                        <img [type]="'leadrat'" [appImage]="media.image" alt="social media"
                                            class="br-50 mr-10 mb-10" height="24" width="24"
                                            *ngIf="hasSocialMedia(media.name)" />
                                    </a>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="qrFormData?.footer?.footerDesign === 2  && screen?.innerWidth > 480">
                    <div class="px-20 justify-between justify-content-center">
                        <div class="w-25 align-end">
                            <div class="text-center text-xs"> © {{currentYear}} {{subDomain}} All Rights Reserved
                            </div>
                        </div>
                        <div class="justify-center-col align-center-col w-50">
                            <img *ngIf="qrFormData?.footer?.logoUrl"
                                [appImage]="qrFormData?.footer?.logoUrl ? s3BucketUrl+qrFormData?.footer?.logoUrl : ''"
                                [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80"
                                height="80">
                        </div>
                        <div class="w-25">
                            <div class="d-flex ph-flex-col" *ngIf="qrFormData?.address"><span
                                    class="fw-600 mr-8">Address</span><span
                                    class="text-xs word-break fw-semi-bold mt-2">{{qrFormData?.address}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.phoneNumber">
                                <span class="fw-600 mr-8">Phone</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.phoneNumber}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.email">
                                <span class="fw-600 mr-8 text-nowrap">Email</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.email}}</span>
                            </div>
                        </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="qrFormData?.footer?.footerDesign === 3  && screen?.innerWidth > 480">
                    <div class="px-20 justify-between">
                        <div class="w-25">
                            <div class="d-flex ph-flex-col" *ngIf="qrFormData?.address"><span
                                    class="fw-600 mr-8">Address</span><span
                                    class="text-xs word-break fw-semi-bold mt-2">{{qrFormData?.address}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.phoneNumber">
                                <span class="fw-600 mr-8">Phone</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.phoneNumber}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.email">
                                <span class="fw-600 mr-8 text-nowrap">Email</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold fw-semi-bold">{{qrFormData?.email}}</span>
                            </div>
                        </div>
                        <div class="justify-center-col align-center-col w-50">
                            <div class="ph-flex-col align-center">
                                <ng-container *ngFor="let media of socialMedia">
                                    <a *ngIf="hasSocialMedia(media.name)" [href]="getSocialMediaLink(media.name)"
                                        target="_blank">
                                        <img [type]="'leadrat'" [appImage]="media.image" alt="social media"
                                            class="br-50 mr-10 mb-10" height="24" width="24"
                                            *ngIf="hasSocialMedia(media.name)" />
                                    </a>
                                </ng-container>
                            </div>
                            <div class="text-center text-xs mt-10"> © {{currentYear}} {{subDomain}} All
                                Rights Reserved </div>
                        </div>
                        <div> <img *ngIf="qrFormData?.footer?.logoUrl"
                                [appImage]="qrFormData?.footer?.logoUrl ? s3BucketUrl+qrFormData?.footer?.logoUrl : ''"
                                [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80"
                                height="80">
                        </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="qrFormData?.footer?.footerDesign === 4  && screen?.innerWidth > 480">
                    <div class="px-20 justify-between">
                        <div class="w-25">
                            <div class="d-flex ph-flex-col" *ngIf="qrFormData?.address"><span
                                    class="fw-600 mr-8">Address</span><span
                                    class="text-xs word-break fw-semi-bold mt-2">{{qrFormData?.address}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.phoneNumber">
                                <span class="fw-600 mr-8">Phone</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.phoneNumber}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.email">
                                <span class="fw-600 mr-8 text-nowrap">Email</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.email}}</span>
                            </div>
                        </div>
                        <div class="justify-center-col align-center-col w-50">
                            <div class="d-flex">
                                <ng-container *ngFor="let media of socialMedia">
                                    <a *ngIf="hasSocialMedia(media.name)" [href]="getSocialMediaLink(media.name)"
                                        target="_blank">
                                        <img [type]="'leadrat'" [appImage]="media.image" alt="social media"
                                            class="br-50 mr-10 mb-10" height="24" width="24"
                                            *ngIf="hasSocialMedia(media.name)" />
                                    </a>
                                </ng-container>
                            </div> <img *ngIf="qrFormData?.footer?.logoUrl"
                                [appImage]="qrFormData?.footer?.logoUrl ? s3BucketUrl+qrFormData?.footer?.logoUrl : ''"
                                [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80"
                                height="80">
                        </div>
                        <div class="w-25 align-items-end justify-center text-center text-xs  ">
                            © {{currentYear}} {{subDomain}} All Rights Reserved </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="qrFormData?.footer?.footerDesign === 5  && screen?.innerWidth > 480">
                    <div class="px-20 justify-between">
                        <div class="flex-center-col w-25">
                            <img *ngIf="qrFormData?.footer?.logoUrl"
                                [appImage]="qrFormData?.footer?.logoUrl ? s3BucketUrl+qrFormData?.footer?.logoUrl : ''"
                                [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80"
                                height="80">
                        </div>
                        <div class="w-50 align-items-end justify-center">
                            <div class="text-center text-xs  "> © {{currentYear}} {{subDomain}} All Rights
                                Reserved </div>
                        </div>
                        <div class="w-25">
                            <div class="d-flex ph-flex-col" *ngIf="qrFormData?.address"><span
                                    class="fw-600 mr-8">Address</span><span
                                    class="text-xs word-break fw-semi-bold mt-2">{{qrFormData?.address}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.phoneNumber">
                                <span class="fw-600 mr-8">Phone</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.phoneNumber}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.email">
                                <span class="fw-600 mr-8 text-nowrap">Email</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.email}}</span>
                            </div>
                        </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="qrFormData?.footer?.footerDesign === 6  && screen?.innerWidth > 480">
                    <div class="px-20 flex-between">
                        <div class="w-25">
                            <div class="d-flex ph-flex-col" *ngIf="qrFormData?.address"><span
                                    class="fw-600 mr-8">Address</span><span
                                    class="text-xs word-break fw-semi-bold mt-2">{{qrFormData?.address}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.phoneNumber">
                                <span class="fw-600 mr-8">Phone</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.phoneNumber}}</span>
                            </div>
                            <div class="mt-20 ph-flex-col align-center" *ngIf="qrFormData?.email">
                                <span class="fw-600 mr-8 text-nowrap">Email</span><span
                                    class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.email}}
                                </span>
                            </div>
                        </div>
                        <div class="w-50">
                            <div class="text-center text-xs"> © {{currentYear}} {{subDomain}} All Rights Reserved
                            </div>
                        </div>
                        <div class="w-25"></div>
                    </div>
                </ng-container>
                <ng-container *ngIf="screen?.innerWidth < 480">
                    <div class="px-20 justify-center-col align-center-col">
                        <img *ngIf="qrFormData?.footer?.logoUrl && qrFormData?.footer?.footerDesign !== 6"
                            [appImage]="qrFormData?.footer?.logoUrl ? s3BucketUrl+qrFormData?.footer?.logoUrl : ''"
                            [type]="'orgProfileLogo'" alt="logo" class="br-50 obj-cover border" width="80" height="80">
                        <div class="d-flex mt-8" *ngIf="qrFormData?.address">
                            <span class="fw-600 mr-8">Address</span>
                            <span class="text-xs word-break fw-semi-bold mt-2">{{qrFormData?.address}}
                            </span>
                        </div>
                        <div class="mt-6 align-center" *ngIf="qrFormData?.phoneNumber">
                            <span class="fw-600 mr-8 text-xxs">Phone</span><span
                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.phoneNumber}}</span>
                        </div>
                        <div class="mt-6 align-center" *ngIf="qrFormData?.email">
                            <span class="fw-600 mr-8 text-xxs">Email</span><span
                                class="text-xs text-truncate-1 break-all fw-semi-bold">{{qrFormData?.email}}</span>
                        </div>
                        <div class="align-center"
                            *ngIf="qrFormData?.footer?.footerDesign !== 2 && qrFormData?.footer?.footerDesign !== 5 && qrFormData?.footer?.footerDesign !== 6">
                            <ng-container *ngFor="let media of socialMedia">
                                <a *ngIf="hasSocialMedia(media.name)" [href]="getSocialMediaLink(media.name)"
                                    target="_blank">
                                    <img [type]="'leadrat'" [appImage]="media.image" alt="social media"
                                        class="br-50 mr-10 mt-6" height="24" width="24"
                                        *ngIf="hasSocialMedia(media.name)" />
                                </a>
                            </ng-container>
                        </div>
                        <div class="text-center text-xs  mt-6"> © {{currentYear}} {{subDomain}} All
                            Rights Reserved </div>

                    </div>
                </ng-container>
            </div>
        </div>
    </div>
    <div class="flex-end px-20 py-16 bg-white position-fixed bottom-0 w-100 shadow">
        <button class="btn-coal" (click)="postData()">Submit</button>
    </div>
</ng-container>

<ng-template #templateNotFound>
    <div class="flex-center-col h-100">
        <img src="assets/images/layered-cards.svg" alt="No Data Found" />
        <h1 class="fw-semi-bold text-center mx-30"> QR code is inactive. Please connect with admin.</h1>
    </div>
</ng-template>